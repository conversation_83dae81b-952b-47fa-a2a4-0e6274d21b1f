/*!******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/products/ProductCard/productCard.module.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************/
/* Product Card Styles - Magazine/Editorial Theme */
.productCard_productCard__xI8yk {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.productCard_productCard__xI8yk:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* Image Container */
.productCard_imageContainer__iDB5f {
  position: relative;
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.productCard_productImage__GNX_E {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.productCard_productCard__xI8yk:hover .productCard_productImage__GNX_E {
  transform: scale(1.05);
}

.productCard_outOfStockOverlay___PQEf {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 1.1rem;
}

/* Product Info */
.productCard_productInfo__iWv4n {
  padding: 1.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.productCard_productName__rwmUL {
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0 0 0.75rem 0;
  line-height: 1.3;
}

.productCard_productDescription__asG12 {
  color: #4b5563;
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0 0 1rem 0;
  flex: 1;
}

/* Price Container */
.productCard_priceContainer__Tw4q6 {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
}

.productCard_priceSection__K5asJ {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.productCard_price__obPDJ {
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 700;
}

.productCard_wholesaleLabel__qv_KS {
  color: #059669;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.productCard_retailPrice__edSAq {
  color: #6b7280;
  font-size: 0.875rem;
  text-decoration: line-through;
}

.productCard_collection__rpw0H {
  color: #1e40af;
  font-size: 0.85rem;
  font-style: italic;
  align-self: flex-end;
}

/* Stock Info */
.productCard_stockInfo__TFFt9 {
  margin-bottom: 1rem;
}

.productCard_inStock__IqfYV {
  color: #059669;
  font-size: 0.85rem;
  font-weight: 600;
}

.productCard_outOfStock__2N_WF {
  color: #dc2626;
  font-size: 0.85rem;
  font-weight: 600;
}

/* Action Buttons */
.productCard_actionButtons__wVj_4 {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: auto;
}

.productCard_viewDetailsBtn__657_H {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1rem;
  background: transparent;
  color: #1e40af;
  border: 2px solid #1e40af;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.productCard_viewDetailsBtn__657_H:hover {
  background: #1e40af;
  color: white;
}

/* Add to Cart Section */
.productCard_addToCartSection__OXbtp {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.productCard_quantitySelector__GmTbx {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.productCard_quantityBtn__x8ZxX {
  width: 32px;
  height: 32px;
  border: 1px solid #d1d5db;
  background: white;
  color: #1f2937;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.productCard_quantityBtn__x8ZxX:hover:not(:disabled) {
  background: #f3f4f6;
  border-color: #1e40af;
}

.productCard_quantityBtn__x8ZxX:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.productCard_quantity__SxGKR {
  min-width: 40px;
  text-align: center;
  font-weight: 600;
  color: #1f2937;
}

.productCard_addToCartBtn__YogtN {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.875rem 1rem;
  background: #1e40af;
  color: white;
  border: none;
  border-radius: 4px;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.productCard_addToCartBtn__YogtN:hover:not(:disabled) {
  background: #1d4ed8;
  transform: translateY(-1px);
}

.productCard_addToCartBtn__YogtN:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.productCard_cartIcon__xaTFW {
  width: 18px;
  height: 18px;
  stroke-width: 2;
}

.productCard_loading__pInZC {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.productCard_loading__pInZC::after {
  content: '';
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: productCard_spin__BTkaQ 1s linear infinite;
}

@keyframes productCard_spin__BTkaQ {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .productCard_productCard__xI8yk {
    margin-bottom: 1rem;
  }
  
  .productCard_imageContainer__iDB5f {
    height: 200px;
  }
  
  .productCard_productInfo__iWv4n {
    padding: 1rem;
  }
  
  .productCard_productName__rwmUL {
    font-size: 1.1rem;
  }
  
  .productCard_price__obPDJ {
    font-size: 1.25rem;
  }
  
  .productCard_actionButtons__wVj_4 {
    gap: 0.5rem;
  }
  
  .productCard_addToCartSection__OXbtp {
    flex-direction: column;
  }
}

/*!******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/products/ProductGrid/productGrid.module.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************/
/* Product Grid Styles - Magazine/Editorial Theme */
.productGrid_productGrid__ZMmDP {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  padding: 2rem 0;
}

/* Loading States */
.productGrid_loadingContainer__I6lQs {
  padding: 2rem 0;
}

.productGrid_loadingGrid__pGK1K {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
}

.productGrid_loadingCard__Y1WSl {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  animation: productGrid_pulse__AKxo6 1.5s ease-in-out infinite;
}

.productGrid_loadingImage__7vn6G {
  width: 100%;
  height: 250px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: productGrid_shimmer__lwSwM 1.5s infinite;
}

.productGrid_loadingContent__9lBxI {
  padding: 1.5rem;
}

.productGrid_loadingTitle__cc1cI {
  height: 1.5rem;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: productGrid_shimmer__lwSwM 1.5s infinite;
  border-radius: 4px;
  margin-bottom: 0.75rem;
}

.productGrid_loadingDescription__d_ugA {
  height: 1rem;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: productGrid_shimmer__lwSwM 1.5s infinite;
  border-radius: 4px;
  margin-bottom: 0.5rem;
  width: 80%;
}

.productGrid_loadingPrice___QJtz {
  height: 1.25rem;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: productGrid_shimmer__lwSwM 1.5s infinite;
  border-radius: 4px;
  margin-bottom: 1rem;
  width: 60%;
}

.productGrid_loadingButton__g0tro {
  height: 2.5rem;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: productGrid_shimmer__lwSwM 1.5s infinite;
  border-radius: 4px;
}

@keyframes productGrid_shimmer__lwSwM {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes productGrid_pulse__AKxo6 {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* Empty State */
.productGrid_emptyContainer__zAwbL {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  min-height: 300px;
}

.productGrid_emptyIcon__8RKzk {
  width: 80px;
  height: 80px;
  color: #d1d5db;
  margin-bottom: 1.5rem;
}

.productGrid_emptyIcon__8RKzk svg {
  width: 100%;
  height: 100%;
}

.productGrid_emptyTitle__HLxmW {
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 0.75rem 0;
}

.productGrid_emptyMessage__uACmE {
  color: #6b5b4d;
  font-size: 1rem;
  line-height: 1.5;
  margin: 0;
  max-width: 400px;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .productGrid_productGrid__ZMmDP,
  .productGrid_loadingGrid__pGK1K {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .productGrid_productGrid__ZMmDP,
  .productGrid_loadingGrid__pGK1K {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
    padding: 1rem 0;
  }
  
  .productGrid_emptyContainer__zAwbL {
    padding: 3rem 1rem;
  }
  
  .productGrid_emptyIcon__8RKzk {
    width: 60px;
    height: 60px;
  }
  
  .productGrid_emptyTitle__HLxmW {
    font-size: 1.25rem;
  }
  
  .productGrid_emptyMessage__uACmE {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .productGrid_productGrid__ZMmDP,
  .productGrid_loadingGrid__pGK1K {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/products/MagazineProductCard/magazineProductCard.module.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************/
/* Magazine-Style Product Card */
.magazineProductCard_productCard__NqE7k {
  background: #ffffff;
  border-radius: 0;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.magazineProductCard_productCard__NqE7k:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* Variants */
.magazineProductCard_featured__HlMJ9 {
  grid-column: span 2;
}

.magazineProductCard_featured__HlMJ9 .magazineProductCard_imageContainer__T7c0A {
  height: 350px;
}

.magazineProductCard_featured__HlMJ9 .magazineProductCard_productInfo__MydeV {
  padding: 2.5rem;
}

.magazineProductCard_featured__HlMJ9 .magazineProductCard_productName__DK2q4 {
  font-size: 1.5rem;
}

.magazineProductCard_compact__uqiq3 .magazineProductCard_imageContainer__T7c0A {
  height: 200px;
}

.magazineProductCard_compact__uqiq3 .magazineProductCard_productInfo__MydeV {
  padding: 1.5rem;
}

.magazineProductCard_compact__uqiq3 .magazineProductCard_productName__DK2q4 {
  font-size: 1.125rem;
}

.magazineProductCard_compact__uqiq3 .magazineProductCard_productDescription__alYFV {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Image Position Variants */
.magazineProductCard_left__JZJnW,
.magazineProductCard_right__8Q2QT {
  flex-direction: row;
  height: 300px;
}

.magazineProductCard_left__JZJnW .magazineProductCard_imageContainer__T7c0A,
.magazineProductCard_right__8Q2QT .magazineProductCard_imageContainer__T7c0A {
  width: 50%;
  height: 100%;
}

.magazineProductCard_left__JZJnW .magazineProductCard_productInfo__MydeV,
.magazineProductCard_right__8Q2QT .magazineProductCard_productInfo__MydeV {
  width: 50%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.magazineProductCard_right__8Q2QT {
  flex-direction: row-reverse;
}

/* Image Container */
.magazineProductCard_imageContainer__T7c0A {
  position: relative;
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.magazineProductCard_productImage__HHRjW {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.magazineProductCard_productCard__NqE7k:hover .magazineProductCard_productImage__HHRjW {
  transform: scale(1.05);
}

.magazineProductCard_imageOverlay__Xtbu_ {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.magazineProductCard_productCard__NqE7k:hover .magazineProductCard_imageOverlay__Xtbu_ {
  opacity: 1;
}

.magazineProductCard_outOfStockOverlay__E2BMn {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 1.125rem;
}

.magazineProductCard_wholesaleBadge__oOh34 {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: #1f2937;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Product Info */
.magazineProductCard_productInfo__MydeV {
  padding: 2rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.magazineProductCard_productHeader__m1LH_ {
  margin-bottom: 1rem;
}

.magazineProductCard_collection__vJ9Po {
  display: inline-block;
  background: #f3f4f6;
  color: #374151;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.75rem;
}

.magazineProductCard_productName__DK2q4 {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.3;
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.magazineProductCard_productDescription__alYFV {
  font-size: 0.95rem;
  color: #6b7280;
  line-height: 1.6;
  margin: 1rem 0;
  flex: 1;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.magazineProductCard_priceContainer__Gt8jW {
  margin-bottom: 1rem;
}

.magazineProductCard_priceSection__ozg_g {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.magazineProductCard_price__RyddO {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
}

.magazineProductCard_retailPrice__AxWqJ {
  font-size: 0.875rem;
  color: #9ca3af;
  text-decoration: line-through;
}

.magazineProductCard_stockInfo__627QZ {
  margin-bottom: 1.5rem;
}

.magazineProductCard_inStock__64qHA {
  color: #059669;
  font-size: 0.875rem;
  font-weight: 600;
}

.magazineProductCard_outOfStock__eNDWo {
  color: #dc2626;
  font-size: 0.875rem;
  font-weight: 600;
}

/* Action Buttons */
.magazineProductCard_actionButtons__SWyCy {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: auto;
}

.magazineProductCard_viewDetailsBtn___OaqJ {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1rem;
  background: transparent;
  color: #1f2937;
  border: 2px solid #e5e7eb;
  border-radius: 0;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: all 0.3s ease;
}

.magazineProductCard_viewDetailsBtn___OaqJ:hover {
  background: #1f2937;
  color: white;
  border-color: #1f2937;
}

.magazineProductCard_addToCartSection__mo3BH {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.magazineProductCard_quantitySelector__lXqyc {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background: #f9fafb;
  padding: 0.5rem;
  border-radius: 6px;
}

.magazineProductCard_quantityBtn__GLo17 {
  width: 32px;
  height: 32px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-weight: 600;
  color: #374151;
  transition: all 0.2s ease;
}

.magazineProductCard_quantityBtn__GLo17:hover:not(:disabled) {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.magazineProductCard_quantityBtn__GLo17:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.magazineProductCard_quantity__a5voe {
  min-width: 40px;
  text-align: center;
  font-weight: 600;
  color: #1f2937;
}

.magazineProductCard_addToCartBtn__tROIJ {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.875rem 1rem;
  background: #1f2937;
  color: white;
  border: none;
  border-radius: 0;
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  cursor: pointer;
  transition: all 0.3s ease;
}

.magazineProductCard_addToCartBtn__tROIJ:hover:not(:disabled) {
  background: #374151;
  transform: translateY(-1px);
}

.magazineProductCard_addToCartBtn__tROIJ:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.magazineProductCard_cartIcon__ByG3T {
  width: 16px;
  height: 16px;
  stroke-width: 2;
}

.magazineProductCard_loading__AbRbj {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .magazineProductCard_featured__HlMJ9 {
    grid-column: span 1;
  }
  
  .magazineProductCard_featured__HlMJ9 .magazineProductCard_imageContainer__T7c0A {
    height: 300px;
  }
  
  .magazineProductCard_featured__HlMJ9 .magazineProductCard_productInfo__MydeV {
    padding: 2rem;
  }
  
  .magazineProductCard_featured__HlMJ9 .magazineProductCard_productName__DK2q4 {
    font-size: 1.375rem;
  }
}

@media (max-width: 768px) {
  .magazineProductCard_left__JZJnW,
  .magazineProductCard_right__8Q2QT {
    flex-direction: column;
    height: auto;
  }
  
  .magazineProductCard_left__JZJnW .magazineProductCard_imageContainer__T7c0A,
  .magazineProductCard_right__8Q2QT .magazineProductCard_imageContainer__T7c0A {
    width: 100%;
    height: 200px;
  }
  
  .magazineProductCard_left__JZJnW .magazineProductCard_productInfo__MydeV,
  .magazineProductCard_right__8Q2QT .magazineProductCard_productInfo__MydeV {
    width: 100%;
  }
  
  .magazineProductCard_productInfo__MydeV {
    padding: 1.5rem;
  }
  
  .magazineProductCard_productName__DK2q4 {
    font-size: 1.125rem;
  }
  
  .magazineProductCard_productDescription__alYFV {
    font-size: 0.9rem;
    -webkit-line-clamp: 2;
  }
}

@media (max-width: 480px) {
  .magazineProductCard_imageContainer__T7c0A {
    height: 200px;
  }
  
  .magazineProductCard_productInfo__MydeV {
    padding: 1rem;
  }
  
  .magazineProductCard_productName__DK2q4 {
    font-size: 1rem;
  }
  
  .magazineProductCard_productDescription__alYFV {
    font-size: 0.875rem;
  }
  
  .magazineProductCard_actionButtons__SWyCy {
    gap: 0.75rem;
  }
  
  .magazineProductCard_addToCartSection__mo3BH {
    gap: 0.5rem;
  }
}

/*!********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/ui/MagazineSection/magazineSection.module.css ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************/
/* Magazine Section - Professional & Minimalist Design */
.magazineSection_magazineSection__ymcRJ {
  padding: 4rem 0;
  background: #ffffff;
  position: relative;
}

.magazineSection_magazineSection__ymcRJ:nth-child(even) {
  background: #fafafa;
}

.magazineSection_container__q3Hum {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.magazineSection_content__3YxyE {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  min-height: 500px;
}

/* Image positioning variants */
.magazineSection_right__ZzDRc .magazineSection_content__3YxyE {
  grid-template-columns: 1fr 1fr;
}

.magazineSection_left__nemOU .magazineSection_content__3YxyE {
  grid-template-columns: 1fr 1fr;
}

.magazineSection_left__nemOU .magazineSection_textContent__0geAI {
  order: 2;
}

.magazineSection_left__nemOU .magazineSection_imageContent__z1_VE {
  order: 1;
}

/* Text Content */
.magazineSection_textContent__0geAI {
  padding: 2rem 0;
  max-width: 500px;
  margin: 0 auto;
}

.magazineSection_badge__gTrqi {
  display: inline-block;
  background: #f3f4f6;
  color: #374151;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 1.5rem;
}

.magazineSection_title__Tx36r {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.2;
  margin-bottom: 1rem;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.magazineSection_subtitle__lxy7W {
  font-size: 1.25rem;
  font-weight: 500;
  color: #6b7280;
  line-height: 1.4;
  margin-bottom: 1.5rem;
}

.magazineSection_description__xp6jK {
  font-size: 1.125rem;
  color: #4b5563;
  line-height: 1.7;
  margin-bottom: 2rem;
  font-weight: 400;
}

.magazineSection_additionalContent__l3qlJ {
  margin-bottom: 2rem;
}

.magazineSection_ctaContainer__QPOaQ {
  margin-top: 2rem;
}

.magazineSection_ctaButton__NHePo {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  background: #1f2937;
  color: white;
  padding: 1rem 2rem;
  border: none;
  border-radius: 0;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.magazineSection_ctaButton__NHePo:hover {
  background: #374151;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.magazineSection_ctaIcon__8g9yP {
  width: 20px;
  height: 20px;
  stroke-width: 2;
  transition: transform 0.3s ease;
}

.magazineSection_ctaButton__NHePo:hover .magazineSection_ctaIcon__8g9yP {
  transform: translateX(4px);
}

/* Image Content */
.magazineSection_imageContent__z1_VE {
  position: relative;
  height: 100%;
  min-height: 400px;
}

.magazineSection_imageContainer__7d9ew {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 0;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.magazineSection_image__EAvCc {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.magazineSection_imageContainer__7d9ew:hover .magazineSection_image__EAvCc {
  transform: scale(1.05);
}

.magazineSection_imageOverlay___zNaM {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.magazineSection_imageContainer__7d9ew:hover .magazineSection_imageOverlay___zNaM {
  opacity: 1;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .magazineSection_content__3YxyE {
    gap: 3rem;
  }
  
  .magazineSection_title__Tx36r {
    font-size: 2.25rem;
  }
}

@media (max-width: 768px) {
  .magazineSection_magazineSection__ymcRJ {
    padding: 3rem 0;
  }
  
  .magazineSection_container__q3Hum {
    padding: 0 1rem;
  }
  
  .magazineSection_content__3YxyE {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }
  
  .magazineSection_left__nemOU .magazineSection_textContent__0geAI,
  .magazineSection_right__ZzDRc .magazineSection_textContent__0geAI {
    order: 2;
  }
  
  .magazineSection_left__nemOU .magazineSection_imageContent__z1_VE,
  .magazineSection_right__ZzDRc .magazineSection_imageContent__z1_VE {
    order: 1;
  }
  
  .magazineSection_textContent__0geAI {
    max-width: 100%;
    padding: 1rem 0;
  }
  
  .magazineSection_title__Tx36r {
    font-size: 2rem;
  }
  
  .magazineSection_description__xp6jK {
    font-size: 1rem;
  }
  
  .magazineSection_imageContent__z1_VE {
    min-height: 300px;
  }
}

@media (max-width: 480px) {
  .magazineSection_magazineSection__ymcRJ {
    padding: 2rem 0;
  }
  
  .magazineSection_title__Tx36r {
    font-size: 1.75rem;
  }
  
  .magazineSection_ctaButton__NHePo {
    padding: 0.875rem 1.5rem;
    font-size: 0.9rem;
  }
  
  .magazineSection_imageContent__z1_VE {
    min-height: 250px;
  }
}

/*!**************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/ui/MagazineGrid/magazineGrid.module.css ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************/
/* Magazine Grid - Professional Layout System */
.magazineGrid_magazineGrid__bl_Wd {
  display: grid;
  width: 100%;
  align-items: stretch;
}

/* Column Variants */
.magazineGrid_columns1__a5AXl {
  grid-template-columns: 1fr;
}

.magazineGrid_columns2__yLGPt {
  grid-template-columns: repeat(2, 1fr);
}

.magazineGrid_columns3__UgXXX {
  grid-template-columns: repeat(3, 1fr);
}

.magazineGrid_columns4__vIdYS {
  grid-template-columns: repeat(4, 1fr);
}

/* Gap Variants */
.magazineGrid_gapsmall__tYH5f {
  gap: 1rem;
}

.magazineGrid_gapmedium__9aGk1 {
  gap: 2rem;
}

.magazineGrid_gaplarge__C8vy6 {
  gap: 3rem;
}

/* Responsive Behavior */
@media (max-width: 1200px) {
  .magazineGrid_columns4__vIdYS {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .magazineGrid_columns3__UgXXX,
  .magazineGrid_columns4__vIdYS {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .magazineGrid_gapmedium__9aGk1 {
    gap: 1.5rem;
  }
  
  .magazineGrid_gaplarge__C8vy6 {
    gap: 2rem;
  }
}

@media (max-width: 480px) {
  .magazineGrid_columns2__yLGPt,
  .magazineGrid_columns3__UgXXX,
  .magazineGrid_columns4__vIdYS {
    grid-template-columns: 1fr;
  }
  
  .magazineGrid_gapsmall__tYH5f {
    gap: 0.75rem;
  }
  
  .magazineGrid_gapmedium__9aGk1 {
    gap: 1rem;
  }
  
  .magazineGrid_gaplarge__C8vy6 {
    gap: 1.5rem;
  }
}

/*!**************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/ui/MagazineCard/magazineCard.module.css ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************/
/* Magazine Card - Clean & Professional Design */
.magazineCard_cardLink__S2LIn,
.magazineCard_cardButton___trMZ {
  display: block;
  text-decoration: none;
  color: inherit;
  border: none;
  background: none;
  padding: 0;
  cursor: pointer;
  width: 100%;
}

.magazineCard_magazineCard__iRLmm {
  background: #ffffff;
  border-radius: 0;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  position: relative;

  /* Perfect Equal Height Cards */
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 450px;
}


.magazineCard_cardLink__S2LIn:hover .magazineCard_magazineCard__iRLmm,
.magazineCard_cardButton___trMZ:hover .magazineCard_magazineCard__iRLmm {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* Image Container - Fixed Height for Perfect Symmetry */
.magazineCard_imageContainer__gsZ0l {
  position: relative;
  width: 100%;
  height: 250px;
  max-height: 200px;
  overflow: hidden;
  flex-shrink: 0; /* Prevent image from shrinking */
}

.magazineCard_image__sdHZR {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.magazineCard_cardLink__S2LIn:hover .magazineCard_image__sdHZR,
.magazineCard_cardButton___trMZ:hover .magazineCard_image__sdHZR {
  transform: scale(1.05);
}

.magazineCard_imageOverlay__h1DuR {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.magazineCard_cardLink__S2LIn:hover .magazineCard_imageOverlay__h1DuR,
.magazineCard_cardButton___trMZ:hover .magazineCard_imageOverlay__h1DuR {
  opacity: 1;
}

.magazineCard_badge__O89mP {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: #ffffff;
  color: #1f2937;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Content - Perfect Symmetrical Layout */
.magazineCard_content__SoZsB {
  padding: 2rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  text-align: left;
  min-height: 150px; /* Ensure consistent content area height */
}

.magazineCard_title__Hdpyn {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.4;
  margin-bottom: 0.75rem;
  min-height: 2.8rem; /* Ensure consistent title height for 2 lines */
  display: flex;
  align-items: flex-start;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.magazineCard_description___n3SW {
  font-size: 0.95rem;
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 1rem;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  min-height: 4.8rem; /* Ensure consistent description height for 3 lines */
  flex-grow: 1;
}

.magazineCard_priceContainer__vv5kq {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.magazineCard_price__YDxxK {
  font-size: 1.125rem;
  font-weight: 700;
  color: #1f2937;
}

.magazineCard_originalPrice__BOUXY {
  font-size: 0.95rem;
  color: #9ca3af;
  text-decoration: line-through;
}

.magazineCard_additionalContent__5EZfH {
  margin-top: auto;
  padding-top: 1rem;
}

/* Variants */
.magazineCard_featured__gD38H {
  grid-column: span 2;
}

.magazineCard_featured__gD38H .magazineCard_imageContainer__gsZ0l {
  height: 350px;
  flex-shrink: 0;
}

.magazineCard_featured__gD38H .magazineCard_content__SoZsB {
  padding: 2.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  min-height: 180px;
}

.magazineCard_featured__gD38H .magazineCard_title__Hdpyn {
  font-size: 1.5rem;
  min-height: 3.2rem;
}

.magazineCard_compact__N9U_X .magazineCard_imageContainer__gsZ0l {
  height: 200px;
}

.magazineCard_compact__N9U_X .magazineCard_content__SoZsB {
  padding: 1.5rem;
}

.magazineCard_compact__N9U_X .magazineCard_title__Hdpyn {
  font-size: 1.125rem;
}

.magazineCard_compact__N9U_X .magazineCard_description___n3SW {
  -webkit-line-clamp: 2;
}

/* Image Position Variants */
.magazineCard_left__LVGdf,
.magazineCard_right__74IkU {
  display: flex;
  height: 300px;
}

.magazineCard_left__LVGdf .magazineCard_imageContainer__gsZ0l,
.magazineCard_right__74IkU .magazineCard_imageContainer__gsZ0l {
  width: 50%;
  height: 100%;
}

.magazineCard_left__LVGdf .magazineCard_content__SoZsB,
.magazineCard_right__74IkU .magazineCard_content__SoZsB {
  width: 50%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.magazineCard_right__74IkU {
  flex-direction: row-reverse;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .magazineCard_featured__gD38H {
    grid-column: span 1;
  }
  
  .magazineCard_featured__gD38H .magazineCard_imageContainer__gsZ0l {
    height: 300px;
    flex-shrink: 0;
  }
  
  .magazineCard_featured__gD38H .magazineCard_content__SoZsB {
    padding: 2rem;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    min-height: 150px;
  }
  
  .magazineCard_featured__gD38H .magazineCard_title__Hdpyn {
    font-size: 1.375rem;
  }
}

@media (max-width: 768px) {
  .magazineCard_left__LVGdf,
  .magazineCard_right__74IkU {
    flex-direction: column;
    height: auto;
  }
  
  .magazineCard_left__LVGdf .magazineCard_imageContainer__gsZ0l,
  .magazineCard_right__74IkU .magazineCard_imageContainer__gsZ0l {
    width: 100%;
    height: 200px;
  }
  
  .magazineCard_left__LVGdf .magazineCard_content__SoZsB,
  .magazineCard_right__74IkU .magazineCard_content__SoZsB {
    width: 100%;
  }
  
  .magazineCard_content__SoZsB {
    padding: 1.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    min-height: 130px;
  }
  
  .magazineCard_title__Hdpyn {
    font-size: 1.125rem;
  }
  
  .magazineCard_description___n3SW {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .magazineCard_imageContainer__gsZ0l {
    height: 200px;
    flex-shrink: 0;
  }

  .magazineCard_content__SoZsB {
    padding: 1rem;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    min-height: 120px;
  }
  
  .magazineCard_title__Hdpyn {
    font-size: 1rem;
    min-height: 2.4rem;
  }

  .magazineCard_description___n3SW {
    font-size: 0.875rem;
    -webkit-line-clamp: 2;
    min-height: 3.2rem;
  }
}

/*!**********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/ui/PlaceholderImage/placeholderImage.module.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************/
/* Placeholder Image Component */
.placeholderImage_placeholder__R4nb2 {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  color: #6b7280;
  transition: all 0.3s ease;
}

.placeholderImage_placeholder__R4nb2:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.placeholderImage_content__el39E {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  text-align: center;
  padding: 1rem;
}

.placeholderImage_icon__8jKzT {
  width: 48px;
  height: 48px;
  stroke-width: 1.5;
  opacity: 0.6;
}

.placeholderImage_text__Wthot {
  font-size: 0.875rem;
  font-weight: 500;
  opacity: 0.8;
}

/* Type Variants */
.placeholderImage_product__nysQ9 {
  background: #fef3f2;
  border-color: #fecaca;
  color: #dc2626;
}

.placeholderImage_product__nysQ9:hover {
  background: #fee2e2;
  border-color: #f87171;
}

.placeholderImage_collection__TwClU {
  background: #f0f9ff;
  border-color: #bae6fd;
  color: #0284c7;
}

.placeholderImage_collection__TwClU:hover {
  background: #e0f2fe;
  border-color: #7dd3fc;
}

.placeholderImage_general__lS2fL {
  background: #f9fafb;
  border-color: #d1d5db;
  color: #6b7280;
}

.placeholderImage_general__lS2fL:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

/* Responsive Design */
@media (max-width: 768px) {
  .placeholderImage_icon__8jKzT {
    width: 32px;
    height: 32px;
  }
  
  .placeholderImage_text__Wthot {
    font-size: 0.75rem;
  }
  
  .placeholderImage_content__el39E {
    gap: 0.5rem;
    padding: 0.75rem;
  }
}

/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/products/MagazineProductGrid/magazineProductGrid.module.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************/
/* Magazine Product Grid */
.magazineProductGrid_productGrid__W6w7E {
  width: 100%;
}

/* Loading States */
.magazineProductGrid_loadingContainer__DBZxS {
  width: 100%;
}

.magazineProductGrid_loadingCard__Pl1G0 {
  background: #ffffff;
  border-radius: 0;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.magazineProductGrid_loadingImage__jJgIB {
  width: 100%;
  height: 250px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: magazineProductGrid_loading__EObpW 1.5s infinite;
}

.magazineProductGrid_loadingContent__sxMZ_ {
  padding: 2rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.magazineProductGrid_loadingBadge__i_iHr {
  width: 80px;
  height: 20px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: magazineProductGrid_loading__EObpW 1.5s infinite;
  border-radius: 10px;
}

.magazineProductGrid_loadingTitle__fsWNP {
  width: 70%;
  height: 24px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: magazineProductGrid_loading__EObpW 1.5s infinite;
  border-radius: 4px;
}

.magazineProductGrid_loadingDescription__KMcri {
  width: 100%;
  height: 60px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: magazineProductGrid_loading__EObpW 1.5s infinite;
  border-radius: 4px;
}

.magazineProductGrid_loadingPrice__hykcw {
  width: 50%;
  height: 20px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: magazineProductGrid_loading__EObpW 1.5s infinite;
  border-radius: 4px;
}

.magazineProductGrid_loadingButton__cgpvJ {
  width: 100%;
  height: 40px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: magazineProductGrid_loading__EObpW 1.5s infinite;
  border-radius: 4px;
  margin-top: auto;
}

@keyframes magazineProductGrid_loading__EObpW {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Empty State */
.magazineProductGrid_emptyContainer__qZj_z {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  color: #6b7280;
}

.magazineProductGrid_emptyIcon__1RP3f {
  width: 80px;
  height: 80px;
  margin-bottom: 2rem;
  background: #f3f4f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.magazineProductGrid_emptyIcon__1RP3f svg {
  width: 40px;
  height: 40px;
  stroke: #9ca3af;
  stroke-width: 2;
}

.magazineProductGrid_emptyTitle__BVxBe {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
}

.magazineProductGrid_emptyMessage__gobK2 {
  font-size: 1rem;
  line-height: 1.6;
  max-width: 400px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .magazineProductGrid_loadingContent__sxMZ_ {
    padding: 1.5rem;
  }
  
  .magazineProductGrid_loadingImage__jJgIB {
    height: 200px;
  }
  
  .magazineProductGrid_emptyContainer__qZj_z {
    padding: 3rem 1rem;
  }
  
  .magazineProductGrid_emptyIcon__1RP3f {
    width: 60px;
    height: 60px;
  }
  
  .magazineProductGrid_emptyIcon__1RP3f svg {
    width: 30px;
    height: 30px;
  }
  
  .magazineProductGrid_emptyTitle__BVxBe {
    font-size: 1.25rem;
  }
  
  .magazineProductGrid_emptyMessage__gobK2 {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .magazineProductGrid_loadingContent__sxMZ_ {
    padding: 1rem;
  }
  
  .magazineProductGrid_loadingImage__jJgIB {
    height: 180px;
  }
  
  .magazineProductGrid_emptyContainer__qZj_z {
    padding: 2rem 1rem;
  }
}

/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/products/ProductImageGallery/productImageGallery.module.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************/
/* Product Image Gallery - Sharp Rectangular Design */
.productImageGallery_imageGallery__n5jcJ {
  width: 100%;
}

/* Main Image Container */
.productImageGallery_mainImageContainer__2d6Gi {
  position: relative;
  background: #f8f8f8;
  border: 2px solid #ddd;
  border-radius: 0; /* Sharp corners */
  overflow: hidden;
  aspect-ratio: 1;
  margin-bottom: 1rem;
}

.productImageGallery_mainImage__de06d {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: zoom-in;
  transition: transform 0.3s ease;
}

.productImageGallery_mainImage__de06d.productImageGallery_zoomed__gW04g {
  cursor: zoom-out;
}

/* Navigation Buttons */
.productImageGallery_navButton__pmcWA {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  width: 50px;
  height: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0; /* Sharp corners */
}

.productImageGallery_navButton__pmcWA:hover {
  background: rgba(0, 0, 0, 0.9);
}

.productImageGallery_prevButton__r4pvX {
  left: 10px;
}

.productImageGallery_nextButton__Ffk19 {
  right: 10px;
}

/* Image Counter */
.productImageGallery_imageCounter__Fn2cW {
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  font-weight: 600;
  border-radius: 0; /* Sharp corners */
}

/* Zoom Indicator */
.productImageGallery_zoomIndicator__w_xth {
  position: absolute;
  top: 15px;
  right: 15px;
  background: rgba(255, 255, 255, 0.9);
  color: #1f2937;
  padding: 0.5rem;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 0; /* Sharp corners */
}

/* Thumbnail Container */
/* .thumbnailContainer {
  margin-top: 1rem;
}

.thumbnailGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 0.5rem;
  max-height: 200px;
  overflow-y: auto;
}

.thumbnail {
  background: none;
  border: 2px solid #ddd;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  transition: all 0.3s ease;
  aspect-ratio: 1;
  overflow: hidden;
}

.thumbnailImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
} */

.productImageGallery_thumbnailContainer__5rAZJ {
  overflow-x: auto;
  overflow-y: hidden;
  white-space: nowrap;
  padding: 8px 0;
}

.productImageGallery_thumbnailGrid__S8J1M {
  display: flex;
  gap: 8px;
}

.productImageGallery_thumbnail__Im2ef {
  display: inline-block;
  border: none;
  background: none;
  padding: 0;
  cursor: pointer;
  outline: none;
}

.productImageGallery_thumbnailImage__1Bb5B {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border: 2px solid transparent;
  border-radius: 4px;
  transition: border 0.3s;
}

.productImageGallery_activeThumbnail__EslMu .productImageGallery_thumbnailImage__1Bb5B {
  border-color: #2563eb; /* Highlight active */
}

.productImageGallery_thumbnail__Im2ef:hover {
  border-color: #2563eb;
}

.productImageGallery_thumbnail__Im2ef.productImageGallery_activeThumbnail__EslMu {
  border-color: #2563eb;
  border-width: 3px;
}



/* Zoom Overlay */
.productImageGallery_zoomOverlay__2Jwxo {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: zoom-out;
}

.productImageGallery_zoomedImageContainer__8vdeq {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
}

.productImageGallery_zoomedImage__2up2d {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.productImageGallery_closeZoomButton__jb9zG {
  position: absolute;
  top: -50px;
  right: 0;
  background: rgba(255, 255, 255, 0.9);
  color: #1f2937;
  border: none;
  width: 40px;
  height: 40px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0; /* Sharp corners */
  transition: background-color 0.3s ease;
}

.productImageGallery_closeZoomButton__jb9zG:hover {
  background: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .productImageGallery_navButton__pmcWA {
    width: 40px;
    height: 40px;
  }
  
  .productImageGallery_prevButton__r4pvX {
    left: 5px;
  }
  
  .productImageGallery_nextButton__Ffk19 {
    right: 5px;
  }
  
  .productImageGallery_imageCounter__Fn2cW {
    bottom: 10px;
    font-size: 0.8rem;
    padding: 0.25rem 0.75rem;
  }
  
  .productImageGallery_zoomIndicator__w_xth {
    top: 10px;
    right: 10px;
    padding: 0.25rem;
    font-size: 0.7rem;
  }
  
  .productImageGallery_thumbnailGrid__S8J1M {
    grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
    gap: 0.25rem;
  }
  
  .productImageGallery_zoomedImageContainer__8vdeq {
    max-width: 95vw;
    max-height: 95vh;
  }
  
  .productImageGallery_closeZoomButton__jb9zG {
    top: -40px;
    width: 35px;
    height: 35px;
  }
}

@media (max-width: 480px) {
  .productImageGallery_mainImageContainer__2d6Gi {
    margin-bottom: 0.5rem;
  }
  
  .productImageGallery_navButton__pmcWA {
    width: 35px;
    height: 35px;
  }
  
  .productImageGallery_thumbnailGrid__S8J1M {
    grid-template-columns: repeat(auto-fit, minmax(50px, 1fr));
  }
}

/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/products/ProductSpecifications/productSpecifications.module.css ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* Product Specifications - Sharp Rectangular Design */
.productSpecifications_specificationsContainer__ExsJP {
  /* margin: 3rem 0; */
  /* margin: 2rem 0 0 0 auto;
  width: 100%;
  max-width: 40vw; */

  margin-top: 2rem;
  /* margin-left: auto; */
  width: 100%;
  /* max-width: 35vw; */
  /* margin-top: -20rem; */
  margin-bottom: 15rem;
}

/* Section Styles */
.productSpecifications_section__yTpdS {
  border: 0px solid #ddd;
  border-radius: 0; /* Sharp corners */
  margin-bottom: 1rem;
  background: white;
}

.productSpecifications_sectionHeader__OfKR9 {
  width: 100%;
  background: #f5f5f5;
  border: none;
  border-bottom: 1px solid #ddd;
  padding: 1.5rem;
  text-align: left;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1.1rem;
  font-weight: 600;
  color:rgb(14, 14, 14);
  transition: background-color 0.3s ease;
  border-radius: 0; /* Sharp corners */
}

.productSpecifications_sectionHeader__OfKR9:hover {
  background: #eeeeee;
}

.productSpecifications_sectionHeader__OfKR9.productSpecifications_active___ctQh {
    /* background: #f5f5f5; */
  background: #f5f5f5;
  color: black;
}

.productSpecifications_toggleIcon__aGGE3 {
  font-size: 1.5rem;
  font-weight: bold;
  transition: transform 0.3s ease;
}

.productSpecifications_sectionContent__lbyYW {
  padding: 2rem;
  border-top: 1px solid #ddd;
}

/* Specifications Grid - Simple Lines Style */
.productSpecifications_specGrid__R0hxs {
  display: block;
  width: 100%;
  background: white;
  margin-top: 1rem;
}
.productSpecifications_keySpecsTable__7L5Ew {
  width: 100%;
  max-width: 600px;
  font-family: "Arial", sans-serif;
  font-size: 15px;
  color: #111;
  border-collapse: collapse;
  margin-top: 2rem;
  border-top: 1px solid #e5e7eb;
}

/* .specRow {
  display: flex;
  align-items: baseline;
  padding: 0.5rem 0;
  border-bottom: 1px solid #e5e7eb;
}

.specRow:last-child {
  border-bottom: none;
}

.specLabel {
  font-weight: 600;
  color: #1f2937;
  font-size: 1rem;
  margin-right: 0.5rem;
  flex-shrink: 0;
}

.specValue {
  color: #1f2937;
  font-size: 1rem;
  flex: 1;
  margin-left: 1rem;
  text-align: left;
} */

.productSpecifications_specRow__CWtye {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 6px 0;
  border-bottom: 1px solid #d1d5db; /* Tailwind gray-300 */
}

.productSpecifications_specRow__CWtye:last-child {
  border-bottom: none;
}

.productSpecifications_specLabel__9qLUs {
  width: 50%;
  font-weight: 600;
  font-size: 0.875rem; /* text-sm */
  color: #111827; /* Tailwind gray-900 */
  line-height: 1.5;
}

.productSpecifications_specValue__w6W_M {
  width: 50%;
  font-size: 0.875rem;
  color: #111827;
  text-align: left;
  line-height: 1.5;
}


.productSpecifications_specValue__w6W_M.productSpecifications_inStock__0aNgb {
  color: #28a745;
  font-weight: 600;
}

.productSpecifications_specValue__w6W_M.productSpecifications_outOfStock__kBj_Z {
  color: #dc3545;
  font-weight: 600;
}

/* Tags */
.productSpecifications_tagContainer__iOz89 {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.productSpecifications_tag__9z9rJ {
  background: #1e40af;
  color: white;
  padding: 0.25rem 0.75rem;
  font-size: 0.8rem;
  border-radius: 0; /* Sharp corners */
  border: 1px solid #1e40af;
}

/* Details Content */
.productSpecifications_detailsContent__4Okl4 {
  line-height: 1.6;
}

.productSpecifications_description__bGf76 {
  color: #555;
  margin-bottom: 2rem;
  font-size: 1rem;
}

.productSpecifications_featureList__hrnf4 h4 {
  color: #1e40af;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.productSpecifications_featureList__hrnf4 ul {
  list-style: none;
  padding: 0;
}

.productSpecifications_featureList__hrnf4 li {
  padding: 0.5rem 0;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
  padding-left: 1.5rem;
}

.productSpecifications_featureList__hrnf4 li:before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #1e40af;
  font-weight: bold;
}

.productSpecifications_featureList__hrnf4 li:last-child {
  border-bottom: none;
}

/* Care Content */
.productSpecifications_careContent__JIGsc {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
}

.productSpecifications_careSection__j_YYe h4,
.productSpecifications_downloadSection__vziAC h4 {
  color: #1e40af;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.productSpecifications_careSection__j_YYe ul {
  list-style: none;
  padding: 0;
}

.productSpecifications_careSection__j_YYe li {
  padding: 0.5rem 0;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
  padding-left: 1.5rem;
}

.productSpecifications_careSection__j_YYe li:before {
  content: '•';
  position: absolute;
  left: 0;
  color: #1e40af;
  font-weight: bold;
}

.productSpecifications_careSection__j_YYe li:last-child {
  border-bottom: none;
}

/* Download Links */
.productSpecifications_downloadLinks__wUENX {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.productSpecifications_downloadLink__eqrqE {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  border: 2px solid #1e40af;
  color: #1e40af;
  text-decoration: none;
  transition: all 0.3s ease;
  border-radius: 0; /* Sharp corners */
  font-weight: 500;
}

.productSpecifications_downloadLink__eqrqE:hover {
  background: #1e40af;
  color: white;
}

/* Share Section */
.productSpecifications_shareSection__Akriu {
  margin-top: 2rem;
  padding: 1.5rem;
  border: 2px solid #ddd;
  border-radius: 0; /* Sharp corners */
  background: #f8f8f8;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.productSpecifications_shareLabel__PZjiC {
  font-weight: 600;
  color: #1e40af;
  font-size: 1rem;
}

.productSpecifications_shareButtons__reB6Z {
  display: flex;
  gap: 0.75rem;
}

.productSpecifications_shareButton__G57wC {
  background: #1e40af;
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border-radius: 0; /* Sharp corners */
}

.productSpecifications_shareButton__G57wC:hover {
  background: #1d4ed8;
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .productSpecifications_careContent__JIGsc {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
}


  @media (max-width: 768px) {
      
  .productSpecifications_shareSection__Akriu {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }
  
  .productSpecifications_shareButtons__reB6Z {
    justify-content: center;
  }  

  .productSpecifications_downloadLink__eqrqE {
    padding: 0.75rem;
    font-size: 0.9rem;
  }
  
  .productSpecifications_shareButton__G57wC {
    width: 35px;
    height: 35px;
  }
  
  .productSpecifications_tagContainer__iOz89 {
    gap: 0.25rem;
  }
  
  .productSpecifications_tag__9z9rJ {
    font-size: 0.75rem;
    padding: 0.2rem 0.5rem;
  }
  .productSpecifications_specRow__CWtye {
    display: flex !important;
    flex-direction: row !important;
    justify-content: space-between;
    align-items: flex-start;
     /* align-items: baseline; */
    gap: 0.5rem;
    grid-template-columns: 1fr;
    flex-wrap: wrap;
    padding: 0.4rem 0;
    border-bottom: 1px solid #e5e7eb;
    line-height: 1.3;
  }

  .productSpecifications_specLabel__9qLUs {
    font-weight: 600;
    flex: 0 0 auto;
    min-width: 100px;
    font-size: 0.9rem;
  }

  .productSpecifications_specGrid__R0hxs {
    display: block;
    margin-top: 0.5rem;
  }


  .productSpecifications_specRow__CWtye:last-child {
    border-bottom: none;
  }

  .productSpecifications_specLabel__9qLUs {
    font-weight: 600;
    color: #1f2937;
    font-size: 0.9rem;
    margin-right: 0.4rem;
    flex-shrink: 0;
  }

  .productSpecifications_specValue__w6W_M {
    color: #1f2937;
    font-size: 0.9rem;
    flex: 1;
    word-break: break-word;
  }

  .productSpecifications_sectionHeader__OfKR9 {
    padding: 1rem;
    font-size: 1rem;
  }

  .productSpecifications_sectionContent__lbyYW {
    padding: 1rem;
  }

}



/*!**************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/products/RelatedProducts/relatedProducts.module.css ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************/
/* Related Products - Sharp Rectangular Design */
.relatedProducts_relatedProducts__AL9MF {
  margin: 4rem 0;
  padding: 2rem 0;
  border-top: 2px solid #ddd;
}

.relatedProducts_sectionHeader__W8wkl {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.relatedProducts_sectionTitle__aMxvg {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

.relatedProducts_scrollControls__U4JgE {
  display: flex;
  gap: 0.5rem;
}

.relatedProducts_scrollButton__XwIzU {
  background: #2563eb;
  color: white;
  border: 2px solid #2563eb;
  width: 45px;
  height: 45px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border-radius: 0; /* Sharp corners */
}

.relatedProducts_scrollButton__XwIzU:hover:not(.relatedProducts_disabled__uoeUy) {
  background: white;
  color: #2563eb;
}

.relatedProducts_scrollButton__XwIzU.relatedProducts_disabled__uoeUy {
  background: #ccc;
  border-color: #ccc;
  cursor: not-allowed;
  opacity: 0.5;
}

/* Products Container */
.relatedProducts_productsContainer__90DuD {
  overflow-x: auto;
  overflow-y: hidden;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.relatedProducts_productsContainer__90DuD::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.relatedProducts_productsGrid__A4_Fq {
  display: flex;
  gap: 1.5rem;
  padding-bottom: 1rem;
}

/* Product Card */
.relatedProducts_productCard__MMP_j {
  flex: 0 0 300px;
  background: white;
  border: 2px solid #ddd;
  border-radius: 0; /* Sharp corners */
  overflow: hidden;
  transition: all 0.3s ease;
}

.relatedProducts_productCard__MMP_j:hover {
  border-color: #2563eb;
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(37, 99, 235, 0.15);
}

.relatedProducts_productLink__jhjXd {
  text-decoration: none;
  color: inherit;
  display: block;
}

/* Image Container */
.relatedProducts_imageContainer__KaBlC {
  position: relative;
  aspect-ratio: 1;
  overflow: hidden;
  background: #f8f8f8;
}

.relatedProducts_productImage__it2PW {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.relatedProducts_productCard__MMP_j:hover .relatedProducts_productImage__it2PW {
  transform: scale(1.05);
}

.relatedProducts_outOfStockOverlay__4FsDH {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 1.1rem;
}

/* Product Info */
.relatedProducts_productInfo__4uK6T {
  padding: 1.5rem;
}

.relatedProducts_productName__D5nFe {
  font-size: 1.2rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 1rem 0;
  line-height: 1.3;
}

.relatedProducts_productDetails__dYa_Q {
  margin-bottom: 1rem;
}

.relatedProducts_productCode__9f972 {
  display: block;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  color: #666;
  background: #f5f5f5;
  padding: 0.25rem 0.5rem;
  border: 1px solid #ddd;
  border-radius: 0; /* Sharp corners */
  margin-bottom: 0.75rem;
  width: fit-content;
}

.relatedProducts_availability__7N8Ht {
  font-size: 0.9rem;
}

.relatedProducts_inStock__fE8y6 {
  color: #28a745;
  font-weight: 600;
}

.relatedProducts_outOfStock__70Ee2 {
  color: #dc3545;
  font-weight: 600;
}

.relatedProducts_priceContainer__BS15X {
  border-top: 1px solid #eee;
  padding-top: 1rem;
}

.relatedProducts_price__nPp4W {
  font-size: 1.3rem;
  font-weight: 700;
  color: #1f2937;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .relatedProducts_productCard__MMP_j {
    flex: 0 0 280px;
  }
  
  .relatedProducts_sectionTitle__aMxvg {
    font-size: 1.75rem;
  }
}

@media (max-width: 768px) {
  .relatedProducts_relatedProducts__AL9MF {
    margin: 3rem 0;
  }
  
  .relatedProducts_sectionHeader__W8wkl {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
  
  .relatedProducts_scrollControls__U4JgE {
    justify-content: center;
  }
  
  .relatedProducts_productCard__MMP_j {
    flex: 0 0 250px;
  }
  
  .relatedProducts_productInfo__4uK6T {
    padding: 1rem;
  }
  
  .relatedProducts_productName__D5nFe {
    font-size: 1.1rem;
  }
  
  .relatedProducts_price__nPp4W {
    font-size: 1.2rem;
  }
  
  .relatedProducts_sectionTitle__aMxvg {
    font-size: 1.5rem;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .relatedProducts_productCard__MMP_j {
    flex: 0 0 220px;
  }
  
  .relatedProducts_productInfo__4uK6T {
    padding: 0.75rem;
  }
  
  .relatedProducts_productName__D5nFe {
    font-size: 1rem;
  }
  
  .relatedProducts_price__nPp4W {
    font-size: 1.1rem;
  }
  
  .relatedProducts_productCode__9f972 {
    font-size: 0.8rem;
    padding: 0.2rem 0.4rem;
  }
  
  .relatedProducts_scrollButton__XwIzU {
    width: 40px;
    height: 40px;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .relatedProducts_productCard__MMP_j {
    transition: none;
  }
  
  .relatedProducts_productCard__MMP_j:hover {
    transform: none;
  }
  
  .relatedProducts_productImage__it2PW {
    transition: none;
  }
  
  .relatedProducts_productCard__MMP_j:hover .relatedProducts_productImage__it2PW {
    transform: none;
  }
}

/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/collections/FullScreenBanner/fullScreenBanner.module.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************/
/* Full Screen Banner Component */
.fullScreenBanner_fullScreenBanner__d7RfP {
  margin-top: 4rem;
  width: 100%;
  padding: 0 0 5rem 0;  /* Left, Right & Bottom Padding */
  position: relative;
  margin-top: -4.2rem; /* Offset the page padding to make truly full screen */
}

/* Image Container - Full Screen */
.fullScreenBanner_imageContainer__YlWNI {
  margin-top: 5rem;
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.fullScreenBanner_bannerImage__Loq3R {
  object-fit: cover;
  object-position: center;
  transition: transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.fullScreenBanner_bannerImage__Loq3R:hover {
  transform: scale(1.02);
}

/* Overlay - Minimal for text readability only */
.fullScreenBanner_overlay__WgzLL {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0.1) 0%,
    rgba(0, 0, 0, 0.2) 50%,
    rgba(0, 0, 0, 0.3) 100%
  );
  z-index: 1;
}

/* Content Overlay */
.fullScreenBanner_contentOverlay__ODcZ2 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  padding: 2rem;
}

.fullScreenBanner_content__eW1GJ {
  text-align: center;
  color: white;
  max-width: 800px;
  width: 100%;
}

/* Badge */
.fullScreenBanner_badge__M7rQM {
  display: inline-block;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 50px;
  font-size: 0.875rem;
  font-weight: 600;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  margin-bottom: 2rem;
  animation: fullScreenBanner_fadeInUp__DlxbO 0.8s ease-out 0.2s both;
}

/* Title */
.fullScreenBanner_title__f3S6z {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: clamp(3rem, 8vw, 6rem);
  font-weight: 700;
  line-height: 1.1;
  margin: 0;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  animation: fullScreenBanner_fadeInUp__DlxbO 0.8s ease-out 0.4s both;
  letter-spacing: -0.02em;
}

/* Description Section */
.fullScreenBanner_descriptionSection__Yjsw3 {
  background: linear-gradient(135deg, #faf9f7 0%, #ffffff 100%);
  padding: 4rem 0;
  position: relative;
}

.fullScreenBanner_descriptionContainer__blvm0 {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  text-align: center;
}

.fullScreenBanner_description__bjGx0 {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 1.25rem;
  line-height: 1.8;
  color: #374151;
  max-width: 800px;
  margin: 0 auto;
  font-weight: 400;
  animation: fullScreenBanner_fadeInUp__DlxbO 0.8s ease-out 0.6s both;
}

/* Animations */
@keyframes fullScreenBanner_fadeInUp__DlxbO {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .fullScreenBanner_title__f3S6z {
    font-size: clamp(2.5rem, 7vw, 5rem);
  }
  
  .fullScreenBanner_description__bjGx0 {
    font-size: 1.125rem;
  }
  
  .fullScreenBanner_descriptionSection__Yjsw3 {
    padding: 3rem 0;
  }
}

@media (max-width: 768px) {
  .fullScreenBanner_imageContainer__YlWNI {
    height: 80vh;
  }
  
  .fullScreenBanner_contentOverlay__ODcZ2 {
    padding: 1.5rem;
  }
  
  .fullScreenBanner_title__f3S6z {
    font-size: clamp(2rem, 6vw, 4rem);
  }
  
  .fullScreenBanner_badge__M7rQM {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
    margin-bottom: 1.5rem;
  }
  
  .fullScreenBanner_description__bjGx0 {
    font-size: 1rem;
    line-height: 1.6;
  }
  
  .fullScreenBanner_descriptionSection__Yjsw3 {
    padding: 2.5rem 0;
  }
  
  .fullScreenBanner_descriptionContainer__blvm0 {
    padding: 0 1.5rem;
  }
}

@media (max-width: 480px) {
  .fullScreenBanner_imageContainer__YlWNI {
    height: 70vh;
  }
  
  .fullScreenBanner_contentOverlay__ODcZ2 {
    padding: 1rem;
  }
  
  .fullScreenBanner_title__f3S6z {
    font-size: clamp(1.75rem, 5vw, 3rem);
  }
  
  .fullScreenBanner_description__bjGx0 {
    font-size: 0.95rem;
  }
  
  .fullScreenBanner_descriptionSection__Yjsw3 {
    padding: 2rem 0;
  }
}

/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/collections/CollectionsCollage/CollectionCollage.module.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************/
.CollectionCollage_collectionsCollage__eHjC5 {
  padding: 6rem 0;
  background: linear-gradient(135deg, #ffffff 0%, #faf9f7 100%);
  position: relative;
}

.CollectionCollage_container__bT7z5 {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.CollectionCollage_header__Vk7Ra {
  text-align: center;
  margin-bottom: 4rem;
}

.CollectionCollage_title__0AivW {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 3rem;
  font-weight: 700;
  color: #1e3a8a;
  margin-bottom: 1rem;
}

.CollectionCollage_subtitle__7MKqU {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 1.125rem;
  color: #1e40af;
  max-width: 600px;
  margin: 0 auto;
}

/* Dynamic Collage Grid */
.CollectionCollage_collageGrid__9FGqR {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  grid-auto-rows: 200px;
  gap: 1rem;
}

/* Collection Cards */
.CollectionCollage_collectionCard__ghrHq {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  background: transparent;
  transition: all 0.3s ease;
  text-decoration: none;
  color: inherit;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  grid-row: span 1;
}

.CollectionCollage_collectionCard__ghrHq.CollectionCollage_large__7W6LD {
  grid-column: span 2;
  grid-row: span 2;
}

.CollectionCollage_collectionCard__ghrHq.CollectionCollage_medium__A8zUR {
  grid-column: span 2;
  grid-row: span 1;
}

.CollectionCollage_imageContainer__s7CbY {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.CollectionCollage_collectionImage__PqKEE {
  object-fit: cover;
  object-position: center;
  transition: transform 0.6s ease;
}

.CollectionCollage_collectionCard__ghrHq:hover .CollectionCollage_collectionImage__PqKEE {
  transform: scale(1.05);
}

.CollectionCollage_overlay__wfN7w {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
}

.CollectionCollage_cardContent__fK6Fk {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 1.5rem;
  color: white;
  z-index: 2;
}

.CollectionCollage_collectionName__JmoRG {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 1.5rem;
  font-weight: 700;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.CollectionCollage_large__7W6LD .CollectionCollage_collectionName__JmoRG {
  font-size: 2rem;
}

.CollectionCollage_medium__A8zUR .CollectionCollage_collectionName__JmoRG {
  font-size: 1.75rem;
}

.CollectionCollage_exploreButton__5Q3mC {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s ease;
}

.CollectionCollage_collectionCard__ghrHq:hover .CollectionCollage_exploreButton__5Q3mC {
  opacity: 1;
  transform: translateY(0);
}

.CollectionCollage_arrow__vCbAn {
  width: 16px;
  height: 16px;
  transition: transform 0.3s ease;
}

.CollectionCollage_collectionCard__ghrHq:hover .CollectionCollage_arrow__vCbAn {
  transform: translateX(4px);
}

/* Responsive */
@media (max-width: 1024px) {
  .CollectionCollage_collectionCard__ghrHq.CollectionCollage_large__7W6LD,
  .CollectionCollage_collectionCard__ghrHq.CollectionCollage_medium__A8zUR {
    grid-column: span 1;
    grid-row: span 1;
  }
}

/*!****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./node_modules/swiper/swiper.css ***!
  \****************************************************************************************************************************************************************************************************************************************************************************/
/**
 * Swiper 11.2.10
 * Most modern mobile touch slider and framework with hardware accelerated transitions
 * https://swiperjs.com
 *
 * Copyright 2014-2025 Vladimir Kharlampidi
 *
 * Released under the MIT License
 *
 * Released on: June 28, 2025
 */

/* FONT_START */
@font-face {
  font-family: 'swiper-icons';
  src: url('data:application/font-woff;charset=utf-8;base64, 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');
  font-weight: 400;
  font-style: normal;
}
/* FONT_END */
:root {
  --swiper-theme-color: #007aff;
  /*
  --swiper-preloader-color: var(--swiper-theme-color);
  --swiper-wrapper-transition-timing-function: initial;
  */
}
:host {
  position: relative;
  display: block;
  margin-left: auto;
  margin-right: auto;
  z-index: 1;
}
.swiper {
  margin-left: auto;
  margin-right: auto;
  position: relative;
  overflow: hidden;
  list-style: none;
  padding: 0;
  /* Fix of Webkit flickering */
  z-index: 1;
  display: block;
}
.swiper-vertical > .swiper-wrapper {
  flex-direction: column;
}
.swiper-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 1;
  display: flex;
  transition-property: transform;
  transition-timing-function: var(--swiper-wrapper-transition-timing-function, initial);
  box-sizing: content-box;
}
.swiper-android .swiper-slide,
.swiper-ios .swiper-slide,
.swiper-wrapper {
  transform: translate3d(0px, 0, 0);
}
.swiper-horizontal {
  touch-action: pan-y;
}
.swiper-vertical {
  touch-action: pan-x;
}
.swiper-slide {
  flex-shrink: 0;
  width: 100%;
  height: 100%;
  position: relative;
  transition-property: transform;
  display: block;
}
.swiper-slide-invisible-blank {
  visibility: hidden;
}
/* Auto Height */
.swiper-autoheight,
.swiper-autoheight .swiper-slide {
  height: auto;
}
.swiper-autoheight .swiper-wrapper {
  align-items: flex-start;
  transition-property: transform, height;
}
.swiper-backface-hidden .swiper-slide {
  transform: translateZ(0);
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
}
/* 3D Effects */
.swiper-3d.swiper-css-mode .swiper-wrapper {
  perspective: 1200px;
}
.swiper-3d .swiper-wrapper {
  transform-style: preserve-3d;
}
.swiper-3d {
  perspective: 1200px;
}
.swiper-3d .swiper-slide,
.swiper-3d .swiper-cube-shadow {
  transform-style: preserve-3d;
}
/* CSS Mode */
.swiper-css-mode > .swiper-wrapper {
  overflow: auto;
  scrollbar-width: none;
  /* For Firefox */
  -ms-overflow-style: none;
  /* For Internet Explorer and Edge */
}
.swiper-css-mode > .swiper-wrapper::-webkit-scrollbar {
  display: none;
}
.swiper-css-mode > .swiper-wrapper > .swiper-slide {
  scroll-snap-align: start start;
}
.swiper-css-mode.swiper-horizontal > .swiper-wrapper {
  scroll-snap-type: x mandatory;
}
.swiper-css-mode.swiper-vertical > .swiper-wrapper {
  scroll-snap-type: y mandatory;
}
.swiper-css-mode.swiper-free-mode > .swiper-wrapper {
  scroll-snap-type: none;
}
.swiper-css-mode.swiper-free-mode > .swiper-wrapper > .swiper-slide {
  scroll-snap-align: none;
}
.swiper-css-mode.swiper-centered > .swiper-wrapper::before {
  content: '';
  flex-shrink: 0;
  order: 9999;
}
.swiper-css-mode.swiper-centered > .swiper-wrapper > .swiper-slide {
  scroll-snap-align: center center;
  scroll-snap-stop: always;
}
.swiper-css-mode.swiper-centered.swiper-horizontal > .swiper-wrapper > .swiper-slide:first-child {
  margin-inline-start: var(--swiper-centered-offset-before);
}
.swiper-css-mode.swiper-centered.swiper-horizontal > .swiper-wrapper::before {
  height: 100%;
  min-height: 1px;
  width: var(--swiper-centered-offset-after);
}
.swiper-css-mode.swiper-centered.swiper-vertical > .swiper-wrapper > .swiper-slide:first-child {
  margin-block-start: var(--swiper-centered-offset-before);
}
.swiper-css-mode.swiper-centered.swiper-vertical > .swiper-wrapper::before {
  width: 100%;
  min-width: 1px;
  height: var(--swiper-centered-offset-after);
}
/* Slide styles start */
/* 3D Shadows */
.swiper-3d .swiper-slide-shadow,
.swiper-3d .swiper-slide-shadow-left,
.swiper-3d .swiper-slide-shadow-right,
.swiper-3d .swiper-slide-shadow-top,
.swiper-3d .swiper-slide-shadow-bottom,
.swiper-3d .swiper-slide-shadow,
.swiper-3d .swiper-slide-shadow-left,
.swiper-3d .swiper-slide-shadow-right,
.swiper-3d .swiper-slide-shadow-top,
.swiper-3d .swiper-slide-shadow-bottom {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}
.swiper-3d .swiper-slide-shadow {
  background: rgba(0, 0, 0, 0.15);
}
.swiper-3d .swiper-slide-shadow-left {
  background-image: linear-gradient(to left, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
}
.swiper-3d .swiper-slide-shadow-right {
  background-image: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
}
.swiper-3d .swiper-slide-shadow-top {
  background-image: linear-gradient(to top, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
}
.swiper-3d .swiper-slide-shadow-bottom {
  background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
}
.swiper-lazy-preloader {
  width: 42px;
  height: 42px;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -21px;
  margin-top: -21px;
  z-index: 10;
  transform-origin: 50%;
  box-sizing: border-box;
  border: 4px solid var(--swiper-preloader-color, var(--swiper-theme-color));
  border-radius: 50%;
  border-top-color: transparent;
}
.swiper:not(.swiper-watch-progress) .swiper-lazy-preloader,
.swiper-watch-progress .swiper-slide-visible .swiper-lazy-preloader {
  animation: swiper-preloader-spin 1s infinite linear;
}
.swiper-lazy-preloader-white {
  --swiper-preloader-color: #fff;
}
.swiper-lazy-preloader-black {
  --swiper-preloader-color: #000;
}
@keyframes swiper-preloader-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
/* Slide styles end */

/*!**********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./node_modules/swiper/modules/effect-coverflow.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************/

/*!****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./node_modules/swiper/modules/pagination.css ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************/
:root {
  /*
  --swiper-pagination-color: var(--swiper-theme-color);
  --swiper-pagination-left: auto;
  --swiper-pagination-right: 8px;
  --swiper-pagination-bottom: 8px;
  --swiper-pagination-top: auto;
  --swiper-pagination-fraction-color: inherit;
  --swiper-pagination-progressbar-bg-color: rgba(0,0,0,0.25);
  --swiper-pagination-progressbar-size: 4px;
  --swiper-pagination-bullet-size: 8px;
  --swiper-pagination-bullet-width: 8px;
  --swiper-pagination-bullet-height: 8px;
  --swiper-pagination-bullet-border-radius: 50%;
  --swiper-pagination-bullet-inactive-color: #000;
  --swiper-pagination-bullet-inactive-opacity: 0.2;
  --swiper-pagination-bullet-opacity: 1;
  --swiper-pagination-bullet-horizontal-gap: 4px;
  --swiper-pagination-bullet-vertical-gap: 6px;
  */
}
.swiper-pagination {
  position: absolute;
  text-align: center;
  transition: 300ms opacity;
  transform: translate3d(0, 0, 0);
  z-index: 10;
}
.swiper-pagination.swiper-pagination-hidden {
  opacity: 0;
}
.swiper-pagination-disabled > .swiper-pagination,
.swiper-pagination.swiper-pagination-disabled {
  display: none !important;
}
/* Common Styles */
.swiper-pagination-fraction,
.swiper-pagination-custom,
.swiper-horizontal > .swiper-pagination-bullets,
.swiper-pagination-bullets.swiper-pagination-horizontal {
  bottom: var(--swiper-pagination-bottom, 8px);
  top: var(--swiper-pagination-top, auto);
  left: 0;
  width: 100%;
}
/* Bullets */
.swiper-pagination-bullets-dynamic {
  overflow: hidden;
  font-size: 0;
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  transform: scale(0.33);
  position: relative;
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active {
  transform: scale(1);
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-main {
  transform: scale(1);
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev {
  transform: scale(0.66);
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev-prev {
  transform: scale(0.33);
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next {
  transform: scale(0.66);
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next-next {
  transform: scale(0.33);
}
.swiper-pagination-bullet {
  width: var(--swiper-pagination-bullet-width, var(--swiper-pagination-bullet-size, 8px));
  height: var(--swiper-pagination-bullet-height, var(--swiper-pagination-bullet-size, 8px));
  display: inline-block;
  border-radius: var(--swiper-pagination-bullet-border-radius, 50%);
  background: var(--swiper-pagination-bullet-inactive-color, #000);
  opacity: var(--swiper-pagination-bullet-inactive-opacity, 0.2);
}
button.swiper-pagination-bullet {
  border: none;
  margin: 0;
  padding: 0;
  box-shadow: none;
  -webkit-appearance: none;
          appearance: none;
}
.swiper-pagination-clickable .swiper-pagination-bullet {
  cursor: pointer;
}
.swiper-pagination-bullet:only-child {
  display: none !important;
}
.swiper-pagination-bullet-active {
  opacity: var(--swiper-pagination-bullet-opacity, 1);
  background: var(--swiper-pagination-color, var(--swiper-theme-color));
}
.swiper-vertical > .swiper-pagination-bullets,
.swiper-pagination-vertical.swiper-pagination-bullets {
  right: var(--swiper-pagination-right, 8px);
  left: var(--swiper-pagination-left, auto);
  top: 50%;
  transform: translate3d(0px, -50%, 0);
}
.swiper-vertical > .swiper-pagination-bullets .swiper-pagination-bullet,
.swiper-pagination-vertical.swiper-pagination-bullets .swiper-pagination-bullet {
  margin: var(--swiper-pagination-bullet-vertical-gap, 6px) 0;
  display: block;
}
.swiper-vertical > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic,
.swiper-pagination-vertical.swiper-pagination-bullets.swiper-pagination-bullets-dynamic {
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
}
.swiper-vertical > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet,
.swiper-pagination-vertical.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  display: inline-block;
  transition: 200ms transform,
        200ms top;
}
.swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet,
.swiper-pagination-horizontal.swiper-pagination-bullets .swiper-pagination-bullet {
  margin: 0 var(--swiper-pagination-bullet-horizontal-gap, 4px);
}
.swiper-horizontal > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic,
.swiper-pagination-horizontal.swiper-pagination-bullets.swiper-pagination-bullets-dynamic {
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
}
.swiper-horizontal > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet,
.swiper-pagination-horizontal.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  transition: 200ms transform,
        200ms left;
}
.swiper-horizontal.swiper-rtl > .swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  transition: 200ms transform,
    200ms right;
}
/* Fraction */
.swiper-pagination-fraction {
  color: var(--swiper-pagination-fraction-color, inherit);
}
/* Progress */
.swiper-pagination-progressbar {
  background: var(--swiper-pagination-progressbar-bg-color, rgba(0, 0, 0, 0.25));
  position: absolute;
}
.swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
  background: var(--swiper-pagination-color, var(--swiper-theme-color));
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  transform: scale(0);
  transform-origin: left top;
}
.swiper-rtl .swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
  transform-origin: right top;
}
.swiper-horizontal > .swiper-pagination-progressbar,
.swiper-pagination-progressbar.swiper-pagination-horizontal,
.swiper-vertical > .swiper-pagination-progressbar.swiper-pagination-progressbar-opposite,
.swiper-pagination-progressbar.swiper-pagination-vertical.swiper-pagination-progressbar-opposite {
  width: 100%;
  height: var(--swiper-pagination-progressbar-size, 4px);
  left: 0;
  top: 0;
}
.swiper-vertical > .swiper-pagination-progressbar,
.swiper-pagination-progressbar.swiper-pagination-vertical,
.swiper-horizontal > .swiper-pagination-progressbar.swiper-pagination-progressbar-opposite,
.swiper-pagination-progressbar.swiper-pagination-horizontal.swiper-pagination-progressbar-opposite {
  width: var(--swiper-pagination-progressbar-size, 4px);
  height: 100%;
  left: 0;
  top: 0;
}
.swiper-pagination-lock {
  display: none;
}

/*!****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./node_modules/swiper/modules/navigation.css ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************/
:root {
  --swiper-navigation-size: 44px;
  /*
  --swiper-navigation-top-offset: 50%;
  --swiper-navigation-sides-offset: 10px;
  --swiper-navigation-color: var(--swiper-theme-color);
  */
}
.swiper-button-prev,
.swiper-button-next {
  position: absolute;
  top: var(--swiper-navigation-top-offset, 50%);
  width: calc(var(--swiper-navigation-size) / 44 * 27);
  height: var(--swiper-navigation-size);
  margin-top: calc(0px - (var(--swiper-navigation-size) / 2));
  z-index: 10;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--swiper-navigation-color, var(--swiper-theme-color));
}
.swiper-button-prev.swiper-button-disabled,
.swiper-button-next.swiper-button-disabled {
  opacity: 0.35;
  cursor: auto;
  pointer-events: none;
}
.swiper-button-prev.swiper-button-hidden,
.swiper-button-next.swiper-button-hidden {
  opacity: 0;
  cursor: auto;
  pointer-events: none;
}
.swiper-navigation-disabled .swiper-button-prev,
.swiper-navigation-disabled .swiper-button-next {
  display: none !important;
}
.swiper-button-prev svg,
.swiper-button-next svg {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transform-origin: center;
}
.swiper-rtl .swiper-button-prev svg,
.swiper-rtl .swiper-button-next svg {
  transform: rotate(180deg);
}
.swiper-button-prev,
.swiper-rtl .swiper-button-next {
  left: var(--swiper-navigation-sides-offset, 10px);
  right: auto;
}
.swiper-button-next,
.swiper-rtl .swiper-button-prev {
  right: var(--swiper-navigation-sides-offset, 10px);
  left: auto;
}
.swiper-button-lock {
  display: none;
}
/* Navigation font start */
.swiper-button-prev:after,
.swiper-button-next:after {
  font-family: swiper-icons;
  font-size: var(--swiper-navigation-size);
  text-transform: none !important;
  letter-spacing: 0;
  font-variant: initial;
  line-height: 1;
}
.swiper-button-prev:after,
.swiper-rtl .swiper-button-next:after {
  content: 'prev';
}
.swiper-button-next,
.swiper-rtl .swiper-button-prev {
  right: var(--swiper-navigation-sides-offset, 10px);
  left: auto;
}
.swiper-button-next:after,
.swiper-rtl .swiper-button-prev:after {
  content: 'next';
}
/* Navigation font end */

/*!***************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/collections/MasonryCollage/MasonryCollage.module.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************/
.MasonryCollage_collectionsCarousel__npEha {
  position: relative;
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  /* background: linear-gradient(135deg, #1e40af, #1c1651ff, #32327cff);  overflow: hidden; */
  background: url('/images/CollectionBackground3.jpg') center center/cover no-repeat;
  padding: 4rem 0;
}


.MasonryCollage_container__EvAj_ {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.MasonryCollage_header__rP59T {
  text-align: center;
  margin-bottom: 4rem;
  z-index: 10;
  position: relative;
}

.MasonryCollage_title__MDftU {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 3rem;
  font-weight: 700;
  color: white;
  margin-bottom: 1rem;
  text-shadow: none;
}

.MasonryCollage_subtitle__5uJNk {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 1.125rem;
  color: white;
  max-width: 600px;
  margin: 0 auto;
  text-shadow: none;
}

.MasonryCollage_swiperContainer__ZOZJV {
  width: 80vw; /* Full viewport width */
  max-width: 80vw;
  position: relative;
  overflow: visible; /* Allow slides to overflow */
  margin: 0 calc(-40vw + 50%); /* Negative margin to break out of parent container */
}

.MasonryCollage_swiper__DDHTb {
  width: 100%;
  padding-top: 50px;
  padding-bottom: 50px;
}

/* Navigation Arrows */
.MasonryCollage_swiper__DDHTb .swiper-button-next,
.MasonryCollage_swiper__DDHTb .swiper-button-prev {
  color: #333 !important;
  background: rgba(255, 255, 255, 0.95) !important;
  width: 60px !important;
  height: 60px !important;
  border-radius: 50% !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
  transition: all 0.3s ease !important;
  margin-top: -30px !important;
  border: 2px solid #e0e0e0 !important;
  z-index: 20 !important;
}

.MasonryCollage_swiper__DDHTb .swiper-button-next {
  right: 10px !important; /* Adjust value (10px, 0px) as needed */
}

/* Move Prev Button Close to Left Edge */
.MasonryCollage_swiper__DDHTb .swiper-button-prev {
  left: 10px !important;  /* Adjust value (10px, 0px) as needed */
}

.MasonryCollage_swiper__DDHTb .swiper-button-next:hover,
.MasonryCollage_swiper__DDHTb .swiper-button-prev:hover {
  background: rgba(255, 255, 255, 1) !important;
  transform: scale(1.1) !important;
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.25) !important;
  border-color: #333 !important;
}

.MasonryCollage_swiper__DDHTb .swiper-button-next:after,
.MasonryCollage_swiper__DDHTb .swiper-button-prev:after {
  font-size: 20px !important;
  font-weight: bold !important;
  color: #333 !important;
}

.MasonryCollage_swiper__DDHTb .swiper-button-disabled {
  opacity: 0.3 !important;
  pointer-events: none !important;
  cursor: not-allowed !important;
  background: rgba(255, 255, 255, 0.5) !important;
}

/* Pagination */
.MasonryCollage_swiper__DDHTb .swiper-pagination {
  bottom: 20px !important;
  position: relative !important;
  margin-top: 30px !important;
}

.MasonryCollage_swiper__DDHTb .swiper-pagination-bullet {
  background: #ccc !important;
  opacity: 1 !important;
  width: 14px !important;
  height: 14px !important;
  margin: 0 8px !important;
  transition: all 0.3s ease !important;
  border: 2px solid #fff !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.MasonryCollage_swiper__DDHTb .swiper-pagination-bullet-active {
  background: white !important;
  transform: scale(1.1) !important;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.2) !important;
}

.MasonryCollage_swiperSlide__jN10a {
  width: 390px;
  height: 480px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  border-radius: 15px;
  display: flex;
  flex-direction: column;
  justify-content: end;
  align-items: self-start;
  transition: all 0.4s ease;
  transform: scale(0.85);
  opacity: 1;
  background: none;
    margin-bottom: 4rem;

}

.MasonryCollage_swiperSlide__jN10a.MasonryCollage_swiper-slide-active__M9HkE {
  transform: scale(1);
  opacity: 1;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.4);
}

.MasonryCollage_slideLink__ljub6 {
  display: block;
  width: 100%;
  height: 100%;
  text-decoration: none;
  color: inherit;
}

.MasonryCollage_slideBackground__X4KkB {
  width: 100%;
  height: 100%;
  background: none;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  border-radius: 15px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  overflow: hidden;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

.MasonryCollage_categoryTag__WIBM8 {
  text-transform: uppercase;
  color: #fff;
  background: #1b7402;
  padding: 7px 18px 7px 25px;
  display: inline-block;
  border-radius: 0 20px 20px 0px;
  letter-spacing: 2px;
  font-size: 0.8rem;
  font-family: "Open Sans", sans-serif;
  font-weight: 600;
  align-self: flex-start;
}

.MasonryCollage_category-0__uwAGU {
  background: #62667f;
}

.MasonryCollage_category-1__C94xy {
  background: #087ac4;
}

.MasonryCollage_category-2__MVXD_ {
  background: #b45205;
}

.MasonryCollage_category-3__0rSr1 {
  background: #1b7402;
}

.MasonryCollage_category-4__y33EX {
  background: #8b5cf6;
}

.MasonryCollage_slideContent__dx6KP {
  padding: 25px;
  z-index: 2;
  position: relative;
  /* background: linear-gradient(to top, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.3) 50%, transparent 100%); */
  border-radius: 0 0 15px 15px;
  background: transparent;
}

.MasonryCollage_collectionTitle__2GznB {
  color: #fff;
  font-family: "Roboto", sans-serif;
  font-weight: 600;
  font-size: 1.3rem;
  line-height: 1.4;
  margin-bottom: 15px;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.8);
}

.MasonryCollage_collectionLocation__Z_bFO {
  color: #fff;
  font-family: "Roboto", sans-serif;
  font-weight: 400;
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  text-shadow: 0 1px 5px rgba(0, 0, 0, 0.8);
}

.MasonryCollage_locationIcon__YTvmU {
  color: #fff;
  width: 22px;
  height: 22px;
  margin-right: 7px;
  flex-shrink: 0;
}

.MasonryCollage_emptyState__IZJh6 {
  text-align: center;
  padding: 4rem 2rem;
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.125rem;
}

/* Swiper Pagination - handled via global CSS */

/* Responsive Design */
@media (max-width: 1024px) {
  .MasonryCollage_collectionsCarousel__npEha {
    padding: 3rem 0;
  }

  .MasonryCollage_title__MDftU {
    font-size: 2.5rem;
  }

  .MasonryCollage_swiperSlide__jN10a {
    width: 300px;
    height: 400px;
  }
}

@media (max-width: 768px) {
  .MasonryCollage_collectionsCarousel__npEha {
    padding: 2rem 0;
    min-height: 80vh;
  }

  .MasonryCollage_container__EvAj_ {
    padding: 0 1rem;
  }

  .MasonryCollage_title__MDftU {
    font-size: 2rem;
  }

  .MasonryCollage_subtitle__5uJNk {
    font-size: 1rem;
  }

  .MasonryCollage_header__rP59T {
    margin-bottom: 3rem;
  }

  .MasonryCollage_swiperSlide__jN10a {
    width: 280px;
    height: 380px;
  }

  .MasonryCollage_collectionTitle__2GznB {
    font-size: 1.1rem;
  }

  .MasonryCollage_collectionLocation__Z_bFO {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .MasonryCollage_collectionsCarousel__npEha {
    padding: 1.5rem 0;
    min-height: 70vh;
  }

  .MasonryCollage_title__MDftU {
    font-size: 1.75rem;
  }

  .MasonryCollage_header__rP59T {
    margin-bottom: 2rem;
  }

  .MasonryCollage_swiperSlide__jN10a {
    width: 260px;
    height: 360px;
  }

  .MasonryCollage_slideContent__dx6KP {
    padding: 20px;
  }

  .MasonryCollage_collectionTitle__2GznB {
    font-size: 1rem;
    margin-bottom: 10px;
  }

  .MasonryCollage_collectionLocation__Z_bFO {
    font-size: 0.75rem;
  }

  .MasonryCollage_categoryTag__WIBM8 {
    font-size: 0.7rem;
    padding: 5px 15px 5px 20px;
  }

  .MasonryCollage_locationIcon__YTvmU {
    width: 18px;
    height: 18px;
  }
}



/*!*******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/collections/CTASection/ctaSection.module.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************/
/* CTA Section Component */
.ctaSection_ctaSection__5XHiF {
  position: relative;
  height: 33vh; /* 1/3 height as requested */
  min-height: 300px;
  max-height: 500px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Background Container */
.ctaSection_backgroundContainer__apF2n {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
}

.ctaSection_backgroundImage___u1GT {
  object-fit: cover;
  object-position: center;
  transition: transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.ctaSection_ctaSection__5XHiF:hover .ctaSection_backgroundImage___u1GT {
  transform: scale(1.02);
}

/* Overlay */
.ctaSection_overlay__c8_m5 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  /* background: linear-gradient(
    135deg,
    rgba(30, 58, 138, 0.8) 0%,
    rgba(30, 64, 175, 0.7) 50%,
    rgba(37, 99, 235, 0.6) 100%
  ); */
  background: transparent;
  z-index: 1;
}

/* Content */
.ctaSection_content__6Z9EU {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.ctaSection_container__18YGT {
  max-width: 800px;
  text-align: center;
  color: white;
}

/* Title */
.ctaSection_title__FT6sx {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: clamp(2rem, 5vw, 3rem);
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  letter-spacing: -0.02em;
  animation: ctaSection_fadeInUp__wzBii 0.8s ease-out 0.2s both;
}

/* Description */
.ctaSection_description__MrmpR {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: clamp(1rem, 2.5vw, 1.25rem);
  line-height: 1.6;
  margin-bottom: 2rem;
  opacity: 0.95;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  animation: ctaSection_fadeInUp__wzBii 0.8s ease-out 0.4s both;
}

/* CTA Button */
.ctaSection_ctaButton__n9e0F {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  animation: ctaSection_fadeInUp__wzBii 0.8s ease-out 0.6s both;
  position: relative;
  overflow: hidden;
}

.ctaSection_ctaButton__n9e0F::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s ease;
}

.ctaSection_ctaButton__n9e0F:hover::before {
  left: 100%;
}

.ctaSection_ctaButton__n9e0F:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.ctaSection_ctaButton__n9e0F:active {
  transform: translateY(0);
}

/* Button Icon */
.ctaSection_buttonIcon__KaZQh {
  width: 20px;
  height: 20px;
  transition: transform 0.3s ease;
}

.ctaSection_ctaButton__n9e0F:hover .ctaSection_buttonIcon__KaZQh {
  transform: translateX(4px);
}

/* Animations */
@keyframes ctaSection_fadeInUp__wzBii {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .ctaSection_ctaSection__5XHiF {
    height: 40vh;
    min-height: 280px;
  }
  
  .ctaSection_content__6Z9EU {
    padding: 1.5rem;
  }
}

@media (max-width: 768px) {
  .ctaSection_ctaSection__5XHiF {
    height: 45vh;
    min-height: 250px;
  }
  
  .ctaSection_content__6Z9EU {
    padding: 1rem;
  }
  
  .ctaSection_title__FT6sx {
    margin-bottom: 0.75rem;
  }
  
  .ctaSection_description__MrmpR {
    margin-bottom: 1.5rem;
  }
  
  .ctaSection_ctaButton__n9e0F {
    padding: 0.875rem 1.75rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .ctaSection_ctaSection__5XHiF {
    height: 50vh;
    min-height: 220px;
  }
  
  .ctaSection_content__6Z9EU {
    padding: 0.75rem;
  }
  
  .ctaSection_description__MrmpR {
    margin-bottom: 1.25rem;
  }
  
  .ctaSection_ctaButton__n9e0F {
    padding: 0.75rem 1.5rem;
    font-size: 0.85rem;
    gap: 0.5rem;
  }
  
  .ctaSection_buttonIcon__KaZQh {
    width: 18px;
    height: 18px;
  }
}

/*!**********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/app/collections/[id]/collectionPage.module.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************/
/* Magazine-Style Collection Page */
.collectionPage_collectionPage__dpWwW {
  min-height: 100vh;
  background: #ffffff;
  color: #1f2937;
  padding-top: 4.2rem;
}

/* New Collection Page Design (4 Sections) */
.collectionPage_newCollectionPage__Y8u6g {
  min-height: 100vh;
  background: #ffffff;
  color: #1f2937;
  padding-top: 4.2rem;
  overflow-x: hidden; /* Prevent horizontal scroll */
}

/* Ensure smooth transitions between sections */
.collectionPage_newCollectionPage__Y8u6g > * {
  position: relative;
  z-index: 1;
}

/* Add subtle shadows between sections for depth */
.collectionPage_newCollectionPage__Y8u6g > *:not(:first-child) {
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.05);
}

.collectionPage_container___A3BI {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem 2rem;
}

/* Hero Section Styling */
.collectionPage_heroSection__ez1TH {
  background: #fafafa;
}

/* Loading States */
.collectionPage_loadingContainer__WBWJh {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  color: #1e40af;
}

.collectionPage_loadingSpinner__t6N6G {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1e40af;
  border-radius: 50%;
  animation: collectionPage_spin__AaW8Y 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes collectionPage_spin__AaW8Y {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.collectionPage_errorContainer__CNrhZ {
  text-align: center;
  padding: 4rem 2rem;
  color: #1e40af;
}

.collectionPage_errorContainer__CNrhZ h1 {
  font-size: 2rem;
  margin-bottom: 1rem;
}

/* Products Section */
.collectionPage_productsSection__s5zI7 {
  padding: 4rem 0;
  background: #ffffff;
}

.collectionPage_productsContainer__45AMK {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Section Header */
.collectionPage_sectionHeader__06zp7 {
  text-align: center;
  margin-bottom: 3rem;
}

.collectionPage_sectionTitle__E0V4g {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 1rem;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.collectionPage_sectionSubtitle__EDsGL {
  font-size: 1.125rem;
  color: #6b7280;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

/* Products Grid */
.collectionPage_productsGrid__ei2ss {
  margin-top: 2rem;
}

/* Collection Header */
.collectionPage_collectionHeader__7uNhW {
  margin-bottom: 3rem;
  padding: 2rem;
  background: #f8f8f8;
  border: 2px solid #ddd;
  border-radius: 0; /* Sharp corners */
}

.collectionPage_headerContent__fdNxH {
  text-align: center;
}

.collectionPage_collectionTitle__6w_wT {
  font-size: 3rem;
  font-weight: 700;
  color: #1e40af;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.collectionPage_collectionDescription__RlIUg {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 1.5rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.collectionPage_collectionMeta__XNauU {
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.collectionPage_productCount__Duq3_ {
  font-weight: 600;
  color: #1e40af;
  background: white;
  padding: 0.5rem 1rem;
  border: 2px solid #1e40af;
  border-radius: 0; /* Sharp corners */
}

.collectionPage_collectionLevel__Rw6wB {
  font-weight: 600;
  color: #666;
  background: white;
  padding: 0.5rem 1rem;
  border: 2px solid #ddd;
  border-radius: 0; /* Sharp corners */
}

/* Filter Section */
.collectionPage_filterSection__Ts87H {
  margin-bottom: 2rem;
  background: white;
  border: 2px solid #ddd;
  border-radius: 0; /* Sharp corners */
  overflow: hidden;
}

.collectionPage_searchBar__M3c5x {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: #fafafa;
  border-bottom: 1px solid #ddd;
}

.collectionPage_searchInput__FNDqe {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
}

.collectionPage_searchIcon__k6QGR {
  position: absolute;
  left: 1rem;
  color: #666;
  z-index: 1;
}

.collectionPage_searchField__yLznb {
  width: 100%;
  padding: 1rem 1rem 1rem 3rem;
  border: 2px solid #ddd;
  border-radius: 0; /* Sharp corners */
  font-size: 1rem;
  background: white;
  transition: border-color 0.3s ease;
}

.collectionPage_searchField__yLznb:focus {
  outline: none;
  border-color: #1e40af;
}

.collectionPage_filterToggle__EM7PK {
  background: #1f2937;
  color: white;
  border: none;
  padding: 12px 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
  border-radius: 6px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-size: 0.875rem;
}

.collectionPage_filterToggle__EM7PK:hover {
  background: #374151;
  transform: translateY(-1px);
}

.collectionPage_filterToggle__EM7PK.collectionPage_active__2osrc {
  background: #374151;
}

.collectionPage_resultsCount__9zPt_ {
  color: #6b7280;
  font-size: 0.9rem;
  font-weight: 600;
  white-space: nowrap;
}

/* Advanced Filters */
.collectionPage_advancedFilters__Jbs1v {
  padding: 2rem;
  background: white;
  border-top: 1px solid #ddd;
}

.collectionPage_filterGrid__Stgcz {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  align-items: end;
}

.collectionPage_filterGroup__YkSpj {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.collectionPage_filterLabel__Jitrc {
  font-weight: 600;
  color: #1e40af;
  font-size: 0.9rem;
}

.collectionPage_priceRange__A7LrI {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.collectionPage_priceInput__tFv2n {
  flex: 1;
  padding: 0.75rem;
  border: 2px solid #ddd;
  border-radius: 0; /* Sharp corners */
  font-size: 0.9rem;
}

.collectionPage_priceInput__tFv2n:focus {
  outline: none;
  border-color: #1e40af;
}

.collectionPage_checkboxLabel__5NEtD {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #1e40af;
  cursor: pointer;
}

.collectionPage_checkbox__4dK_G {
  width: 18px;
  height: 18px;
  accent-color: #1e40af;
}

.collectionPage_sortControls__wBBIW {
  display: flex;
  gap: 0.5rem;
}

.collectionPage_sortSelect__gT3YY {
  flex: 1;
  padding: 0.75rem;
  border: 2px solid #ddd;
  border-radius: 0; /* Sharp corners */
  background: white;
  font-size: 0.9rem;
}

.collectionPage_sortSelect__gT3YY:focus {
  outline: none;
  border-color: #1e40af;
}

.collectionPage_sortDirection__huoJQ {
  background: #1e40af;
  color: white;
  border: 2px solid #1e40af;
  width: 40px;
  height: 40px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border-radius: 0; /* Sharp corners */
}

.collectionPage_sortDirection__huoJQ:hover {
  background: white;
  color: #1e40af;
}

.collectionPage_sortDirection__huoJQ.collectionPage_desc__B6Uht {
  transform: rotate(180deg);
}

.collectionPage_clearFilters__vVC6G {
  background: #dc3545;
  color: white;
  border: 2px solid #dc3545;
  padding: 0.75rem 1rem;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  border-radius: 0; /* Sharp corners */
  width: 100%;
}

.collectionPage_clearFilters__vVC6G:hover {
  background: white;
  color: #dc3545;
}

/* Products Section */
.collectionPage_productsSection__s5zI7 {
  margin-top: 2rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .collectionPage_collectionTitle__6w_wT {
    font-size: 2.5rem;
  }
  
  .collectionPage_filterGrid__Stgcz {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .collectionPage_container___A3BI {
    padding: 1rem;
  }
  
  .collectionPage_collectionHeader__7uNhW {
    padding: 1.5rem;
    margin-bottom: 2rem;
  }
  
  .collectionPage_collectionTitle__6w_wT {
    font-size: 2rem;
  }
  
  .collectionPage_collectionDescription__RlIUg {
    font-size: 1rem;
  }
  
  .collectionPage_collectionMeta__XNauU {
    flex-direction: column;
    gap: 1rem;
  }
  
  .collectionPage_searchBar__M3c5x {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }
  
  .collectionPage_searchInput__FNDqe {
    width: 100%;
  }
  
  .collectionPage_filterToggle__EM7PK {
    width: 100%;
    justify-content: center;
  }
  
  .collectionPage_filterGrid__Stgcz {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .collectionPage_priceRange__A7LrI {
    flex-direction: column;
    align-items: stretch;
  }
  
  .collectionPage_sortControls__wBBIW {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .collectionPage_collectionTitle__6w_wT {
    font-size: 1.75rem;
  }
  
  .collectionPage_advancedFilters__Jbs1v {
    padding: 1rem;
  }
  
  .collectionPage_searchField__yLznb {
    padding: 0.75rem 0.75rem 0.75rem 2.5rem;
  }
  
  .collectionPage_filterToggle__EM7PK {
    padding: 0.75rem 1rem;
  }
}

/* Additional Magazine-Style Responsive Design */
@media (max-width: 1024px) {
  .collectionPage_sectionTitle__E0V4g {
    font-size: 2.25rem;
  }
}

@media (max-width: 768px) {
  .collectionPage_productsSection__s5zI7 {
    padding: 3rem 0;
  }

  .collectionPage_productsContainer__45AMK {
    padding: 0 1rem;
  }

  .collectionPage_sectionTitle__E0V4g {
    font-size: 2rem;
  }

  .collectionPage_sectionSubtitle__EDsGL {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .collectionPage_productsSection__s5zI7 {
    padding: 2rem 0;
  }

  .collectionPage_sectionTitle__E0V4g {
    font-size: 1.75rem;
  }

  .collectionPage_sectionSubtitle__EDsGL {
    font-size: 0.95rem;
  }
}

/* Collections Section (for Level 1 & 2 collections) */
.collectionPage_collectionsSection__isnXg {
  padding: 4rem 0;
  background: linear-gradient(135deg, #faf9f7 0%, #ffffff 100%);
}

.collectionPage_collectionsContainer__EZDC0 {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.collectionPage_collectionsGrid__TdNGv {
  margin-top: 3rem;
}

.collectionPage_collectionCard__wq54m {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.collectionPage_collectionCard__wq54m:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Empty State for Collections */
.collectionPage_emptyState__V4Siw {
  text-align: center;
  padding: 4rem 2rem;
  color: #6b7280;
}

.collectionPage_emptyIcon__NUWbm {
  width: 64px;
  height: 64px;
  margin: 0 auto 1.5rem;
  color: #d1d5db;
}

.collectionPage_emptyState__V4Siw h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.collectionPage_emptyState__V4Siw p {
  font-size: 1rem;
  line-height: 1.6;
}


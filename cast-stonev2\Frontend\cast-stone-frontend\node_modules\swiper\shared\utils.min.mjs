import{a as getWindow,g as getDocument}from"./ssr-window.esm.min.mjs";function classesToTokens(e){return void 0===e&&(e=""),e.trim().split(" ").filter((e=>!!e.trim()))}function deleteProps(e){const t=e;Object.keys(t).forEach((e=>{try{t[e]=null}catch(e){}try{delete t[e]}catch(e){}}))}function nextTick(e,t){return void 0===t&&(t=0),setTimeout(e,t)}function now(){return Date.now()}function getComputedStyle(e){const t=getWindow();let n;return t.getComputedStyle&&(n=t.getComputedStyle(e,null)),!n&&e.currentStyle&&(n=e.currentStyle),n||(n=e.style),n}function getTranslate(e,t){void 0===t&&(t="x");const n=getWindow();let r,o,s;const l=getComputedStyle(e);return n.WebKitCSSMatrix?(o=l.transform||l.webkitTransform,o.split(",").length>6&&(o=o.split(", ").map((e=>e.replace(",","."))).join(", ")),s=new n.WebKitCSSMatrix("none"===o?"":o)):(s=l.MozTransform||l.OTransform||l.MsTransform||l.msTransform||l.transform||l.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),r=s.toString().split(",")),"x"===t&&(o=n.WebKitCSSMatrix?s.m41:16===r.length?parseFloat(r[12]):parseFloat(r[4])),"y"===t&&(o=n.WebKitCSSMatrix?s.m42:16===r.length?parseFloat(r[13]):parseFloat(r[5])),o||0}function isObject(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)}function isNode(e){return"undefined"!=typeof window&&void 0!==window.HTMLElement?e instanceof HTMLElement:e&&(1===e.nodeType||11===e.nodeType)}function extend(){const e=Object(arguments.length<=0?void 0:arguments[0]),t=["__proto__","constructor","prototype"];for(let n=1;n<arguments.length;n+=1){const r=n<0||arguments.length<=n?void 0:arguments[n];if(null!=r&&!isNode(r)){const n=Object.keys(Object(r)).filter((e=>t.indexOf(e)<0));for(let t=0,o=n.length;t<o;t+=1){const o=n[t],s=Object.getOwnPropertyDescriptor(r,o);void 0!==s&&s.enumerable&&(isObject(e[o])&&isObject(r[o])?r[o].__swiper__?e[o]=r[o]:extend(e[o],r[o]):!isObject(e[o])&&isObject(r[o])?(e[o]={},r[o].__swiper__?e[o]=r[o]:extend(e[o],r[o])):e[o]=r[o])}}}return e}function setCSSProperty(e,t,n){e.style.setProperty(t,n)}function animateCSSModeScroll(e){let{swiper:t,targetPosition:n,side:r}=e;const o=getWindow(),s=-t.translate;let l,i=null;const a=t.params.speed;t.wrapperEl.style.scrollSnapType="none",o.cancelAnimationFrame(t.cssModeFrameID);const c=n>s?"next":"prev",m=(e,t)=>"next"===c&&e>=t||"prev"===c&&e<=t,u=()=>{l=(new Date).getTime(),null===i&&(i=l);const e=Math.max(Math.min((l-i)/a,1),0),c=.5-Math.cos(e*Math.PI)/2;let d=s+c*(n-s);if(m(d,n)&&(d=n),t.wrapperEl.scrollTo({[r]:d}),m(d,n))return t.wrapperEl.style.overflow="hidden",t.wrapperEl.style.scrollSnapType="",setTimeout((()=>{t.wrapperEl.style.overflow="",t.wrapperEl.scrollTo({[r]:d})})),void o.cancelAnimationFrame(t.cssModeFrameID);t.cssModeFrameID=o.requestAnimationFrame(u)};u()}function getSlideTransformEl(e){return e.querySelector(".swiper-slide-transform")||e.shadowRoot&&e.shadowRoot.querySelector(".swiper-slide-transform")||e}function elementChildren(e,t){void 0===t&&(t="");const n=getWindow(),r=[...e.children];return n.HTMLSlotElement&&e instanceof HTMLSlotElement&&r.push(...e.assignedElements()),t?r.filter((e=>e.matches(t))):r}function elementIsChildOfSlot(e,t){const n=[t];for(;n.length>0;){const t=n.shift();if(e===t)return!0;n.push(...t.children,...t.shadowRoot?t.shadowRoot.children:[],...t.assignedElements?t.assignedElements():[])}}function elementIsChildOf(e,t){const n=getWindow();let r=t.contains(e);if(!r&&n.HTMLSlotElement&&t instanceof HTMLSlotElement){r=[...t.assignedElements()].includes(e),r||(r=elementIsChildOfSlot(e,t))}return r}function showWarning(e){try{return void console.warn(e)}catch(e){}}function createElement(e,t){void 0===t&&(t=[]);const n=document.createElement(e);return n.classList.add(...Array.isArray(t)?t:classesToTokens(t)),n}function elementOffset(e){const t=getWindow(),n=getDocument(),r=e.getBoundingClientRect(),o=n.body,s=e.clientTop||o.clientTop||0,l=e.clientLeft||o.clientLeft||0,i=e===t?t.scrollY:e.scrollTop,a=e===t?t.scrollX:e.scrollLeft;return{top:r.top+i-s,left:r.left+a-l}}function elementPrevAll(e,t){const n=[];for(;e.previousElementSibling;){const r=e.previousElementSibling;t?r.matches(t)&&n.push(r):n.push(r),e=r}return n}function elementNextAll(e,t){const n=[];for(;e.nextElementSibling;){const r=e.nextElementSibling;t?r.matches(t)&&n.push(r):n.push(r),e=r}return n}function elementStyle(e,t){return getWindow().getComputedStyle(e,null).getPropertyValue(t)}function elementIndex(e){let t,n=e;if(n){for(t=0;null!==(n=n.previousSibling);)1===n.nodeType&&(t+=1);return t}}function elementParents(e,t){const n=[];let r=e.parentElement;for(;r;)t?r.matches(t)&&n.push(r):n.push(r),r=r.parentElement;return n}function elementTransitionEnd(e,t){t&&e.addEventListener("transitionend",(function n(r){r.target===e&&(t.call(e,r),e.removeEventListener("transitionend",n))}))}function elementOuterSize(e,t,n){const r=getWindow();return n?e["width"===t?"offsetWidth":"offsetHeight"]+parseFloat(r.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-right":"margin-top"))+parseFloat(r.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-left":"margin-bottom")):e.offsetWidth}function makeElementsArray(e){return(Array.isArray(e)?e:[e]).filter((e=>!!e))}function getRotateFix(e){return t=>Math.abs(t)>0&&e.browser&&e.browser.need3dFix&&Math.abs(t)%90==0?t+.001:t}function setInnerHTML(e,t){void 0===t&&(t=""),"undefined"!=typeof trustedTypes?e.innerHTML=trustedTypes.createPolicy("html",{createHTML:e=>e}).createHTML(t):e.innerHTML=t}export{setCSSProperty as a,elementParents as b,createElement as c,elementOffset as d,elementChildren as e,now as f,getSlideTransformEl as g,elementOuterSize as h,elementIndex as i,classesToTokens as j,getTranslate as k,elementTransitionEnd as l,makeElementsArray as m,nextTick as n,isObject as o,getRotateFix as p,elementStyle as q,elementNextAll as r,setInnerHTML as s,elementPrevAll as t,animateCSSModeScroll as u,showWarning as v,elementIsChildOf as w,extend as x,deleteProps as y};
//# sourceMappingURL=utils.min.mjs.map
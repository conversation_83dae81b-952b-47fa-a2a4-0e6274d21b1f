"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/collections/[id]/page",{

/***/ "(app-pages-browser)/./src/app/collections/[id]/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/collections/[id]/page.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CollectionPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _services__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services */ \"(app-pages-browser)/./src/services/index.ts\");\n/* harmony import */ var _components_products__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/products */ \"(app-pages-browser)/./src/components/products/index.ts\");\n/* harmony import */ var _components_collections__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/collections */ \"(app-pages-browser)/./src/components/collections/index.ts\");\n/* harmony import */ var _collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./collectionPage.module.css */ \"(app-pages-browser)/./src/app/collections/[id]/collectionPage.module.css\");\n/* harmony import */ var _collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _components_ui_MagazineSection_MagazineSection__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/MagazineSection/MagazineSection */ \"(app-pages-browser)/./src/components/ui/MagazineSection/MagazineSection.tsx\");\n/* eslint-disable @typescript-eslint/no-explicit-any */ /* eslint-disable react-hooks/exhaustive-deps */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// import { MagazineSection, MagazineGrid, MagazineCard } from '@/components/ui';\n// import { TestimonialsSection } from '@/components/Home/TestimonialsSection/TestimonialsSection';\n\n\n\nfunction CollectionPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const collectionId = parseInt(params.id);\n    const [collection, setCollection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [childCollections, setChildCollections] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredProducts, setFilteredProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        search: '',\n        priceRange: {\n            min: 0,\n            max: 10000\n        },\n        inStockOnly: false,\n        sortBy: 'name',\n        sortDirection: 'asc'\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CollectionPage.useEffect\": ()=>{\n            if (collectionId) {\n                fetchData();\n            }\n        }\n    }[\"CollectionPage.useEffect\"], [\n        collectionId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CollectionPage.useEffect\": ()=>{\n            applyFilters();\n        }\n    }[\"CollectionPage.useEffect\"], [\n        products,\n        filters\n    ]);\n    const fetchData = async ()=>{\n        try {\n            setIsLoading(true);\n            setError(null);\n            // Get collection data first\n            const collectionData = await _services__WEBPACK_IMPORTED_MODULE_3__.collectionService.get.getById(collectionId);\n            setCollection(collectionData);\n            // Based on collection level, fetch appropriate data\n            if (collectionData.level === 3) {\n                // Level 3: Show products\n                const productsData = await _services__WEBPACK_IMPORTED_MODULE_3__.productService.get.getByCollection(collectionId);\n                setProducts(productsData);\n                setChildCollections([]);\n                // Set initial price range based on actual products\n                if (productsData.length > 0) {\n                    const prices = productsData.map((p)=>p.price);\n                    const minPrice = Math.min(...prices);\n                    const maxPrice = Math.max(...prices);\n                    setFilters((prev)=>({\n                            ...prev,\n                            priceRange: {\n                                min: minPrice,\n                                max: maxPrice\n                            }\n                        }));\n                }\n            } else {\n                // Level 1 or 2: Show child collections\n                const childCollectionsData = await _services__WEBPACK_IMPORTED_MODULE_3__.collectionService.get.getChildren(collectionId);\n                setChildCollections(childCollectionsData);\n                setProducts([]);\n            }\n        } catch (err) {\n            console.error('Error fetching collection data:', err);\n            setError('Failed to load collection');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const applyFilters = ()=>{\n        let filtered = [\n            ...products\n        ];\n        // Search filter\n        if (filters.search) {\n            filtered = filtered.filter((product)=>{\n                var _product_description;\n                var _product_description_toLowerCase_includes;\n                return product.name.toLowerCase().includes(filters.search.toLowerCase()) || ((_product_description_toLowerCase_includes = (_product_description = product.description) === null || _product_description === void 0 ? void 0 : _product_description.toLowerCase().includes(filters.search.toLowerCase())) !== null && _product_description_toLowerCase_includes !== void 0 ? _product_description_toLowerCase_includes : false);\n            });\n        }\n        // Price range filter\n        filtered = filtered.filter((product)=>product.price >= filters.priceRange.min && product.price <= filters.priceRange.max);\n        // Stock filter\n        if (filters.inStockOnly) {\n            filtered = filtered.filter((product)=>product.stock > 0);\n        }\n        // Sorting\n        filtered.sort((a, b)=>{\n            let comparison = 0;\n            switch(filters.sortBy){\n                case 'name':\n                    comparison = a.name.localeCompare(b.name);\n                    break;\n                case 'price':\n                    comparison = a.price - b.price;\n                    break;\n                case 'newest':\n                    comparison = new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();\n                    break;\n            }\n            return filters.sortDirection === 'desc' ? -comparison : comparison;\n        });\n        setFilteredProducts(filtered);\n    };\n    const handleFilterChange = (newFilters)=>{\n        setFilters((prev)=>({\n                ...prev,\n                ...newFilters\n            }));\n    };\n    const clearFilters = ()=>{\n        setFilters({\n            search: '',\n            priceRange: {\n                min: 0,\n                max: 10000\n            },\n            inStockOnly: false,\n            sortBy: 'name',\n            sortDirection: 'asc'\n        });\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().loadingContainer),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().loadingSpinner)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Loading collection...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n            lineNumber: 156,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !collection) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().errorContainer),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    children: \"Collection Not Found\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: error || 'The requested collection could not be found.'\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n            lineNumber: 165,\n            columnNumber: 7\n        }, this);\n    }\n    // For level 3 collections (products), keep the existing layout\n    if (collection.level === 3) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().collectionPage),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_MagazineSection_MagazineSection__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    title: collection.name,\n                    subtitle: \"Product Collection\",\n                    description: collection.description || \"Discover this beautiful collection of handcrafted cast stone pieces, carefully curated to bring elegance and sophistication to your space.\",\n                    imageSrc: collection.images && collection.images.length > 0 ? collection.images[0] : \"https://images.unsplash.com/photo-1600607687920-4e2a09cf159d?w=1200&h=600&fit=crop&crop=center\",\n                    imageAlt: collection.name,\n                    imagePosition: \"left\",\n                    badge: \"\".concat(filteredProducts.length, \" Products\"),\n                    className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().heroSection)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().container),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().productsSection),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().productsContainer),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().sectionHeader),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().headerContent),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().sectionTitle),\n                                                children: \"Products in this Collection\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().sectionSubtitle),\n                                                children: [\n                                                    \"Explore all the beautiful pieces in the \",\n                                                    collection.name,\n                                                    \" collection\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().filterSection),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().searchBar),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().searchInput),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().searchIcon),\n                                                            width: \"20\",\n                                                            height: \"20\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"none\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    cx: \"11\",\n                                                                    cy: \"11\",\n                                                                    r: \"8\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 208,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M21 21L16.65 16.65\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"2\",\n                                                                    strokeLinecap: \"round\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 209,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"Search products...\",\n                                                            value: filters.search,\n                                                            onChange: (e)=>handleFilterChange({\n                                                                    search: e.target.value\n                                                                }),\n                                                            className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().searchField)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"\".concat((_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().filterToggle), \" \").concat(showFilters ? (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().active) : ''),\n                                                    onClick: ()=>setShowFilters(!showFilters),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"20\",\n                                                            height: \"20\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"none\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                                                                points: \"22,3 2,3 10,12.46 10,19 14,21 14,12.46\",\n                                                                stroke: \"currentColor\",\n                                                                strokeWidth: \"2\",\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 225,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Filters\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().resultsCount),\n                                                    children: [\n                                                        filteredProducts.length,\n                                                        \" \",\n                                                        filteredProducts.length === 1 ? 'product' : 'products'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, this),\n                                        showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().advancedFilters),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().filterGrid),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().filterGroup),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().filterLabel),\n                                                                children: \"Price Range\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 241,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().priceRange),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        placeholder: \"Min\",\n                                                                        value: filters.priceRange.min,\n                                                                        onChange: (e)=>handleFilterChange({\n                                                                                priceRange: {\n                                                                                    ...filters.priceRange,\n                                                                                    min: Number(e.target.value) || 0\n                                                                                }\n                                                                            }),\n                                                                        className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().priceInput)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 243,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"to\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 252,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        placeholder: \"Max\",\n                                                                        value: filters.priceRange.max,\n                                                                        onChange: (e)=>handleFilterChange({\n                                                                                priceRange: {\n                                                                                    ...filters.priceRange,\n                                                                                    max: Number(e.target.value) || 10000\n                                                                                }\n                                                                            }),\n                                                                        className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().priceInput)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 253,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 242,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().filterGroup),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().checkboxLabel),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    checked: filters.inStockOnly,\n                                                                    onChange: (e)=>handleFilterChange({\n                                                                            inStockOnly: e.target.checked\n                                                                        }),\n                                                                    className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().checkbox)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 268,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"In Stock Only\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 267,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().filterGroup),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().filterLabel),\n                                                                children: \"Sort By\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().sortControls),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: filters.sortBy,\n                                                                        onChange: (e)=>handleFilterChange({\n                                                                                sortBy: e.target.value\n                                                                            }),\n                                                                        className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().sortSelect),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"name\",\n                                                                                children: \"Name\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 287,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"price\",\n                                                                                children: \"Price\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 288,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"newest\",\n                                                                                children: \"Newest\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 289,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 282,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"\".concat((_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().sortDirection), \" \").concat(filters.sortDirection === 'desc' ? (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().desc) : ''),\n                                                                        onClick: ()=>handleFilterChange({\n                                                                                sortDirection: filters.sortDirection === 'asc' ? 'desc' : 'asc'\n                                                                            }),\n                                                                        title: \"Sort \".concat(filters.sortDirection === 'asc' ? 'Descending' : 'Ascending'),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            width: \"16\",\n                                                                            height: \"16\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            fill: \"none\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M7 10L12 15L17 10\",\n                                                                                stroke: \"currentColor\",\n                                                                                strokeWidth: \"2\",\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 300,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 299,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 292,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 281,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().filterGroup),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: clearFilters,\n                                                            className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().clearFilters),\n                                                            children: \"Clear All Filters\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().productsGrid),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_products__WEBPACK_IMPORTED_MODULE_4__.MagazineProductGrid, {\n                                        products: filteredProducts,\n                                        isLoading: isLoading,\n                                        showAddToCart: true,\n                                        showViewDetails: true,\n                                        columns: 3,\n                                        emptyMessage: filters.search || filters.inStockOnly || filters.priceRange.min > 0 || filters.priceRange.max < 10000 ? \"No products match your current filters. Try adjusting your search criteria.\" : \"This collection doesn&apos;t have any products yet.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n            lineNumber: 175,\n            columnNumber: 7\n        }, this);\n    }\n    // For level 1 & 2 collections, use the new 4-section design\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().newCollectionPage),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_collections__WEBPACK_IMPORTED_MODULE_5__.FullScreenBanner, {\n                title: collection.name,\n                description: collection.description || \"Discover this beautiful collection of handcrafted cast stone pieces, carefully curated to bring elegance and sophistication to your space.\",\n                imageSrc: collection.images && collection.images.length > 0 ? collection.images[0] : \"https://images.unsplash.com/photo-1600607687920-4e2a09cf159d?w=1200&h=600&fit=crop&crop=center\",\n                imageAlt: collection.name,\n                badge: \"\".concat(childCollections.length, \" \").concat(collection.level === 1 ? 'Categories' : 'Subcategories')\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                lineNumber: 344,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_collections__WEBPACK_IMPORTED_MODULE_5__.MasonryCollage, {\n                collections: childCollections,\n                title: \"\".concat(collection.level === 1 ? 'Categories' : 'Subcategories', \" in \").concat(collection.name),\n                subtitle: \"Explore the \".concat(collection.level === 1 ? 'categories' : 'subcategories', \" within this collection\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                lineNumber: 355,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n        lineNumber: 342,\n        columnNumber: 5\n    }, this);\n}\n_s(CollectionPage, \"DiTsruDziNr0LLNduFLX65NhF+0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams\n    ];\n});\n_c = CollectionPage;\nvar _c;\n$RefreshReg$(_c, \"CollectionPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/collections/[id]/page.tsx\n"));

/***/ })

});
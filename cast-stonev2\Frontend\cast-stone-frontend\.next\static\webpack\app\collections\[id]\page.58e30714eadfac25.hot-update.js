"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/collections/[id]/page",{

/***/ "(app-pages-browser)/./src/app/collections/[id]/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/collections/[id]/page.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CollectionPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _services__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services */ \"(app-pages-browser)/./src/services/index.ts\");\n/* harmony import */ var _components_products__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/products */ \"(app-pages-browser)/./src/components/products/index.ts\");\n/* harmony import */ var _components_collections__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/collections */ \"(app-pages-browser)/./src/components/collections/index.ts\");\n/* harmony import */ var _collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./collectionPage.module.css */ \"(app-pages-browser)/./src/app/collections/[id]/collectionPage.module.css\");\n/* harmony import */ var _collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _components_ui_MagazineSection_MagazineSection__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/MagazineSection/MagazineSection */ \"(app-pages-browser)/./src/components/ui/MagazineSection/MagazineSection.tsx\");\n/* eslint-disable @typescript-eslint/no-explicit-any */ /* eslint-disable react-hooks/exhaustive-deps */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// import { MagazineSection, MagazineGrid, MagazineCard } from '@/components/ui';\n// import { TestimonialsSection } from '@/components/Home/TestimonialsSection/TestimonialsSection';\n\n\n\nfunction CollectionPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const collectionId = parseInt(params.id);\n    const [collection, setCollection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [childCollections, setChildCollections] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredProducts, setFilteredProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        search: '',\n        priceRange: {\n            min: 0,\n            max: 10000\n        },\n        inStockOnly: false,\n        sortBy: 'name',\n        sortDirection: 'asc'\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CollectionPage.useEffect\": ()=>{\n            if (collectionId) {\n                fetchData();\n            }\n        }\n    }[\"CollectionPage.useEffect\"], [\n        collectionId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CollectionPage.useEffect\": ()=>{\n            applyFilters();\n        }\n    }[\"CollectionPage.useEffect\"], [\n        products,\n        filters\n    ]);\n    const fetchData = async ()=>{\n        try {\n            setIsLoading(true);\n            setError(null);\n            // Get collection data first\n            const collectionData = await _services__WEBPACK_IMPORTED_MODULE_3__.collectionService.get.getById(collectionId);\n            setCollection(collectionData);\n            // Based on collection level, fetch appropriate data\n            if (collectionData.level === 3) {\n                // Level 3: Show products\n                const productsData = await _services__WEBPACK_IMPORTED_MODULE_3__.productService.get.getByCollection(collectionId);\n                setProducts(productsData);\n                setChildCollections([]);\n                // Set initial price range based on actual products\n                if (productsData.length > 0) {\n                    const prices = productsData.map((p)=>p.price);\n                    const minPrice = Math.min(...prices);\n                    const maxPrice = Math.max(...prices);\n                    setFilters((prev)=>({\n                            ...prev,\n                            priceRange: {\n                                min: minPrice,\n                                max: maxPrice\n                            }\n                        }));\n                }\n            } else {\n                // Level 1 or 2: Show child collections\n                const childCollectionsData = await _services__WEBPACK_IMPORTED_MODULE_3__.collectionService.get.getChildren(collectionId);\n                setChildCollections(childCollectionsData);\n                setProducts([]);\n            }\n        } catch (err) {\n            console.error('Error fetching collection data:', err);\n            setError('Failed to load collection');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const applyFilters = ()=>{\n        let filtered = [\n            ...products\n        ];\n        // Search filter\n        if (filters.search) {\n            filtered = filtered.filter((product)=>{\n                var _product_description;\n                var _product_description_toLowerCase_includes;\n                return product.name.toLowerCase().includes(filters.search.toLowerCase()) || ((_product_description_toLowerCase_includes = (_product_description = product.description) === null || _product_description === void 0 ? void 0 : _product_description.toLowerCase().includes(filters.search.toLowerCase())) !== null && _product_description_toLowerCase_includes !== void 0 ? _product_description_toLowerCase_includes : false);\n            });\n        }\n        // Price range filter\n        filtered = filtered.filter((product)=>product.price >= filters.priceRange.min && product.price <= filters.priceRange.max);\n        // Stock filter\n        if (filters.inStockOnly) {\n            filtered = filtered.filter((product)=>product.stock > 0);\n        }\n        // Sorting\n        filtered.sort((a, b)=>{\n            let comparison = 0;\n            switch(filters.sortBy){\n                case 'name':\n                    comparison = a.name.localeCompare(b.name);\n                    break;\n                case 'price':\n                    comparison = a.price - b.price;\n                    break;\n                case 'newest':\n                    comparison = new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();\n                    break;\n            }\n            return filters.sortDirection === 'desc' ? -comparison : comparison;\n        });\n        setFilteredProducts(filtered);\n    };\n    const handleFilterChange = (newFilters)=>{\n        setFilters((prev)=>({\n                ...prev,\n                ...newFilters\n            }));\n    };\n    const clearFilters = ()=>{\n        setFilters({\n            search: '',\n            priceRange: {\n                min: 0,\n                max: 10000\n            },\n            inStockOnly: false,\n            sortBy: 'name',\n            sortDirection: 'asc'\n        });\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().loadingContainer),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().loadingSpinner)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Loading collection...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n            lineNumber: 156,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !collection) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().errorContainer),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    children: \"Collection Not Found\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: error || 'The requested collection could not be found.'\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n            lineNumber: 165,\n            columnNumber: 7\n        }, this);\n    }\n    // For level 3 collections (products), keep the existing layout\n    if (collection.level === 3) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().collectionPage),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_MagazineSection_MagazineSection__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    title: collection.name,\n                    subtitle: \"Product Collection\",\n                    description: collection.description || \"Discover this beautiful collection of handcrafted cast stone pieces, carefully curated to bring elegance and sophistication to your space.\",\n                    imageSrc: collection.images && collection.images.length > 0 ? collection.images[0] : \"https://images.unsplash.com/photo-1600607687920-4e2a09cf159d?w=1200&h=600&fit=crop&crop=center\",\n                    imageAlt: collection.name,\n                    imagePosition: \"left\",\n                    badge: \"\".concat(filteredProducts.length, \" Products\"),\n                    className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().heroSection)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().container),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().productsSection),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().productsContainer),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().sectionHeader),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().headerContent),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().sectionTitle),\n                                                children: \"Products in this Collection\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().sectionSubtitle),\n                                                children: [\n                                                    \"Explore all the beautiful pieces in the \",\n                                                    collection.name,\n                                                    \" collection\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().filterSection),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().searchBar),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().searchInput),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().searchIcon),\n                                                            width: \"20\",\n                                                            height: \"20\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"none\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    cx: \"11\",\n                                                                    cy: \"11\",\n                                                                    r: \"8\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 208,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M21 21L16.65 16.65\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"2\",\n                                                                    strokeLinecap: \"round\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 209,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"Search products...\",\n                                                            value: filters.search,\n                                                            onChange: (e)=>handleFilterChange({\n                                                                    search: e.target.value\n                                                                }),\n                                                            className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().searchField)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"\".concat((_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().filterToggle), \" \").concat(showFilters ? (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().active) : ''),\n                                                    onClick: ()=>setShowFilters(!showFilters),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"20\",\n                                                            height: \"20\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"none\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                                                                points: \"22,3 2,3 10,12.46 10,19 14,21 14,12.46\",\n                                                                stroke: \"currentColor\",\n                                                                strokeWidth: \"2\",\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 225,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Filters\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().resultsCount),\n                                                    children: [\n                                                        filteredProducts.length,\n                                                        \" \",\n                                                        filteredProducts.length === 1 ? 'product' : 'products'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, this),\n                                        showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().advancedFilters),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().filterGrid),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().filterGroup),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().filterLabel),\n                                                                children: \"Price Range\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 241,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().priceRange),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        placeholder: \"Min\",\n                                                                        value: filters.priceRange.min,\n                                                                        onChange: (e)=>handleFilterChange({\n                                                                                priceRange: {\n                                                                                    ...filters.priceRange,\n                                                                                    min: Number(e.target.value) || 0\n                                                                                }\n                                                                            }),\n                                                                        className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().priceInput)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 243,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"to\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 252,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        placeholder: \"Max\",\n                                                                        value: filters.priceRange.max,\n                                                                        onChange: (e)=>handleFilterChange({\n                                                                                priceRange: {\n                                                                                    ...filters.priceRange,\n                                                                                    max: Number(e.target.value) || 10000\n                                                                                }\n                                                                            }),\n                                                                        className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().priceInput)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 253,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 242,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().filterGroup),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().checkboxLabel),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    checked: filters.inStockOnly,\n                                                                    onChange: (e)=>handleFilterChange({\n                                                                            inStockOnly: e.target.checked\n                                                                        }),\n                                                                    className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().checkbox)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 268,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"In Stock Only\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 267,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().filterGroup),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().filterLabel),\n                                                                children: \"Sort By\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().sortControls),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: filters.sortBy,\n                                                                        onChange: (e)=>handleFilterChange({\n                                                                                sortBy: e.target.value\n                                                                            }),\n                                                                        className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().sortSelect),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"name\",\n                                                                                children: \"Name\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 287,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"price\",\n                                                                                children: \"Price\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 288,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"newest\",\n                                                                                children: \"Newest\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 289,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 282,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"\".concat((_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().sortDirection), \" \").concat(filters.sortDirection === 'desc' ? (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().desc) : ''),\n                                                                        onClick: ()=>handleFilterChange({\n                                                                                sortDirection: filters.sortDirection === 'asc' ? 'desc' : 'asc'\n                                                                            }),\n                                                                        title: \"Sort \".concat(filters.sortDirection === 'asc' ? 'Descending' : 'Ascending'),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            width: \"16\",\n                                                                            height: \"16\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            fill: \"none\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M7 10L12 15L17 10\",\n                                                                                stroke: \"currentColor\",\n                                                                                strokeWidth: \"2\",\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 300,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 299,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 292,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 281,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().filterGroup),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: clearFilters,\n                                                            className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().clearFilters),\n                                                            children: \"Clear All Filters\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().productsGrid),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_products__WEBPACK_IMPORTED_MODULE_4__.MagazineProductGrid, {\n                                        products: filteredProducts,\n                                        isLoading: isLoading,\n                                        showAddToCart: true,\n                                        showViewDetails: true,\n                                        columns: 3,\n                                        emptyMessage: filters.search || filters.inStockOnly || filters.priceRange.min > 0 || filters.priceRange.max < 10000 ? \"No products match your current filters. Try adjusting your search criteria.\" : \"This collection doesn&apos;t have any products yet.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n            lineNumber: 175,\n            columnNumber: 7\n        }, this);\n    }\n    // For level 1 & 2 collections, use the new 4-section design\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_collectionPage_module_css__WEBPACK_IMPORTED_MODULE_6___default().newCollectionPage),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_collections__WEBPACK_IMPORTED_MODULE_5__.FullScreenBanner, {\n                title: collection.name,\n                description: collection.description || \"Discover this beautiful collection of handcrafted cast stone pieces, carefully curated to bring elegance and sophistication to your space.\",\n                imageSrc: collection.images && collection.images.length > 0 ? collection.images[0] : \"https://images.unsplash.com/photo-1600607687920-4e2a09cf159d?w=1200&h=600&fit=crop&crop=center\",\n                imageAlt: collection.name,\n                badge: \"\".concat(childCollections.length, \" \").concat(collection.level === 1 ? 'Categories' : 'Subcategories')\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                lineNumber: 344,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_collections__WEBPACK_IMPORTED_MODULE_5__.MasonryCollage, {\n                collections: childCollections,\n                title: \"\".concat(collection.level === 1 ? 'Categories' : 'Subcategories', \" in \").concat(collection.name),\n                subtitle: \"Explore the \".concat(collection.level === 1 ? 'categories' : 'subcategories', \" within this collection\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n                lineNumber: 355,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\",\n        lineNumber: 342,\n        columnNumber: 5\n    }, this);\n}\n_s(CollectionPage, \"DiTsruDziNr0LLNduFLX65NhF+0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams\n    ];\n});\n_c = CollectionPage;\nvar _c;\n$RefreshReg$(_c, \"CollectionPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/collections/[id]/page.tsx\n"));

/***/ })

});
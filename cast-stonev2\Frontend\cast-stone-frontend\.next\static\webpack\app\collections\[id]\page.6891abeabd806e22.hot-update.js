/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/collections/[id]/page",{

/***/ "(app-pages-browser)/./src/components/collections/CTASection/ctaSection.module.css":
/*!*********************************************************************!*\
  !*** ./src/components/collections/CTASection/ctaSection.module.css ***!
  \*********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"ctaSection\":\"ctaSection_ctaSection__5XHiF\",\"backgroundContainer\":\"ctaSection_backgroundContainer__apF2n\",\"backgroundImage\":\"ctaSection_backgroundImage___u1GT\",\"overlay\":\"ctaSection_overlay__c8_m5\",\"content\":\"ctaSection_content__6Z9EU\",\"container\":\"ctaSection_container__18YGT\",\"title\":\"ctaSection_title__FT6sx\",\"fadeInUp\":\"ctaSection_fadeInUp__wzBii\",\"description\":\"ctaSection_description__MrmpR\",\"ctaButton\":\"ctaSection_ctaButton__n9e0F\",\"buttonIcon\":\"ctaSection_buttonIcon__KaZQh\"};\n    if(true) {\n      // 1754141891799\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  \nmodule.exports.__checksum = \"4a29e6d4351f\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2NvbGxlY3Rpb25zL0NUQVNlY3Rpb24vY3RhU2VjdGlvbi5tb2R1bGUuY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0Esa0JBQWtCO0FBQ2xCLE9BQU8sSUFBVTtBQUNqQjtBQUNBLHNCQUFzQixtQkFBTyxDQUFDLHdNQUF5SyxjQUFjLHNEQUFzRDtBQUMzUSxNQUFNLFVBQVU7QUFDaEI7QUFDQTtBQUNBO0FBQ0EseUJBQXlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVtZXIgRmFyb29xXFxEZXNrdG9wXFxjYXN0LXN0b25ldjJcXGNhc3Qtc3RvbmV2MlxcRnJvbnRlbmRcXGNhc3Qtc3RvbmUtZnJvbnRlbmRcXHNyY1xcY29tcG9uZW50c1xcY29sbGVjdGlvbnNcXENUQVNlY3Rpb25cXGN0YVNlY3Rpb24ubW9kdWxlLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBleHRyYWN0ZWQgYnkgbWluaS1jc3MtZXh0cmFjdC1wbHVnaW5cbm1vZHVsZS5leHBvcnRzID0ge1wiY3RhU2VjdGlvblwiOlwiY3RhU2VjdGlvbl9jdGFTZWN0aW9uX181WEhpRlwiLFwiYmFja2dyb3VuZENvbnRhaW5lclwiOlwiY3RhU2VjdGlvbl9iYWNrZ3JvdW5kQ29udGFpbmVyX19hcEYyblwiLFwiYmFja2dyb3VuZEltYWdlXCI6XCJjdGFTZWN0aW9uX2JhY2tncm91bmRJbWFnZV9fX3UxR1RcIixcIm92ZXJsYXlcIjpcImN0YVNlY3Rpb25fb3ZlcmxheV9fYzhfbTVcIixcImNvbnRlbnRcIjpcImN0YVNlY3Rpb25fY29udGVudF9fNlo5RVVcIixcImNvbnRhaW5lclwiOlwiY3RhU2VjdGlvbl9jb250YWluZXJfXzE4WUdUXCIsXCJ0aXRsZVwiOlwiY3RhU2VjdGlvbl90aXRsZV9fRlQ2c3hcIixcImZhZGVJblVwXCI6XCJjdGFTZWN0aW9uX2ZhZGVJblVwX193ekJpaVwiLFwiZGVzY3JpcHRpb25cIjpcImN0YVNlY3Rpb25fZGVzY3JpcHRpb25fX01ybXBSXCIsXCJjdGFCdXR0b25cIjpcImN0YVNlY3Rpb25fY3RhQnV0dG9uX19uOWUwRlwiLFwiYnV0dG9uSWNvblwiOlwiY3RhU2VjdGlvbl9idXR0b25JY29uX19LYVpRaFwifTtcbiAgICBpZihtb2R1bGUuaG90KSB7XG4gICAgICAvLyAxNzU0MTQxODkxNzk5XG4gICAgICB2YXIgY3NzUmVsb2FkID0gcmVxdWlyZShcIkM6L1VzZXJzL1VtZXIgRmFyb29xL0Rlc2t0b3AvY2FzdC1zdG9uZXYyL2Nhc3Qtc3RvbmV2Mi9Gcm9udGVuZC9jYXN0LXN0b25lLWZyb250ZW5kL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvbWluaS1jc3MtZXh0cmFjdC1wbHVnaW4vaG1yL2hvdE1vZHVsZVJlcGxhY2VtZW50LmpzXCIpKG1vZHVsZS5pZCwge1wicHVibGljUGF0aFwiOlwiL19uZXh0L1wiLFwiZXNNb2R1bGVcIjpmYWxzZSxcImxvY2Fsc1wiOnRydWV9KTtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShjc3NSZWxvYWQpO1xuICAgICAgXG4gICAgfVxuICBcbm1vZHVsZS5leHBvcnRzLl9fY2hlY2tzdW0gPSBcIjRhMjllNmQ0MzUxZlwiXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/collections/CTASection/ctaSection.module.css\n"));

/***/ })

});